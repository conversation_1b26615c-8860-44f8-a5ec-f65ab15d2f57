// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// EMQX监控控制器
/// </summary>
[ApiDescriptionSettings("EMQX", Name = "MQTT监控", Order = 102)]
public class EMQXMonitorController : IDynamicApiController, ITransient
{
    private readonly EMQXMonitorService _monitorService;
    private readonly ILogger<EMQXMonitorController> _logger;

    public EMQXMonitorController(EMQXMonitorService monitorService, ILogger<EMQXMonitorController> logger)
    {
        _monitorService = monitorService;
        _logger = logger;
    }

    /// <summary>
    /// 获取实时监控数据
    /// </summary>
    /// <returns>实时监控数据</returns>
    [HttpGet("/api/emqx/monitor/realtime")]
    [DisplayName("获取MQTT实时监控数据")]
    public async Task<RealtimeMonitorDto> GetRealtimeDataAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT实时监控数据");
            var data = await _monitorService.GetRealtimeDataAsync();
            _logger.LogInformation("获取MQTT实时监控数据成功");
            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT实时监控数据失败");
            throw Oops.Oh("获取MQTT实时监控数据失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取流量历史数据
    /// </summary>
    /// <param name="hours">小时数</param>
    /// <returns>流量历史数据</returns>
    [HttpGet("/api/emqx/monitor/traffic")]
    [DisplayName("获取MQTT流量历史数据")]
    public async Task<List<MqttTrafficDto>> GetTrafficHistoryAsync(int hours = 24)
    {
        try
        {
            _logger.LogInformation("获取MQTT流量历史数据，Hours: {Hours}", hours);
            var data = await _monitorService.GetTrafficHistoryAsync(hours);
            _logger.LogInformation("获取到 {Count} 条MQTT流量历史数据", data.Count);
            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT流量历史数据失败");
            throw Oops.Oh("获取MQTT流量历史数据失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取主题统计信息
    /// </summary>
    /// <returns>主题统计信息</returns>
    [HttpGet("/api/emqx/monitor/topics")]
    [DisplayName("获取MQTT主题统计信息")]
    public async Task<List<MqttTopicStatisticsDto>> GetTopicStatisticsAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT主题统计信息");
            var data = await _monitorService.GetTopicStatisticsAsync();
            _logger.LogInformation("获取到 {Count} 个MQTT主题统计信息", data.Count);
            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT主题统计信息失败");
            throw Oops.Oh("获取MQTT主题统计信息失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取连接状态分布
    /// </summary>
    /// <returns>连接状态分布</returns>
    [HttpGet("/api/emqx/monitor/connection-status")]
    [DisplayName("获取MQTT连接状态分布")]
    public async Task<List<ConnectionStatusDto>> GetConnectionStatusDistributionAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT连接状态分布");
            var data = await _monitorService.GetConnectionStatusDistributionAsync();
            _logger.LogInformation("获取到 {Count} 种MQTT连接状态分布", data.Count);
            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT连接状态分布失败");
            throw Oops.Oh("获取MQTT连接状态分布失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取消息类型分布
    /// </summary>
    /// <returns>消息类型分布</returns>
    [HttpGet("/api/emqx/monitor/message-types")]
    [DisplayName("获取MQTT消息类型分布")]
    public async Task<List<MessageTypeDto>> GetMessageTypeDistributionAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT消息类型分布");
            var data = await _monitorService.GetMessageTypeDistributionAsync();
            _logger.LogInformation("获取到 {Count} 种MQTT消息类型分布", data.Count);
            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT消息类型分布失败");
            throw Oops.Oh("获取MQTT消息类型分布失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取性能指标
    /// </summary>
    /// <returns>性能指标</returns>
    [HttpGet("/api/emqx/monitor/performance")]
    [DisplayName("获取MQTT性能指标")]
    public async Task<PerformanceMetricsDto> GetPerformanceMetricsAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT性能指标");
            var data = await _monitorService.GetPerformanceMetricsAsync();
            _logger.LogInformation("获取MQTT性能指标成功");
            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT性能指标失败");
            throw Oops.Oh("获取MQTT性能指标失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取告警信息
    /// </summary>
    /// <returns>告警信息列表</returns>
    [HttpGet("/api/emqx/monitor/alerts")]
    [DisplayName("获取MQTT告警信息")]
    public async Task<List<AlertDto>> GetAlertsAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT告警信息");
            var data = await _monitorService.GetAlertsAsync();
            _logger.LogInformation("获取到 {Count} 条MQTT告警信息", data.Count);
            return data;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT告警信息失败");
            throw Oops.Oh("获取MQTT告警信息失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 导出监控报告
    /// </summary>
    /// <param name="input">导出参数</param>
    /// <returns>监控报告</returns>
    [HttpPost("/api/emqx/monitor/export")]
    [DisplayName("导出MQTT监控报告")]
    public async Task<MonitorReportDto> ExportReportAsync(ExportReportInput input)
    {
        try
        {
            _logger.LogInformation("导出MQTT监控报告，StartTime: {StartTime}, EndTime: {EndTime}", input.StartTime, input.EndTime);
            var report = await _monitorService.ExportReportAsync(input.StartTime, input.EndTime);
            _logger.LogInformation("导出MQTT监控报告成功，ReportId: {ReportId}", report.ReportId);
            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "导出MQTT监控报告失败");
            throw Oops.Oh("导出MQTT监控报告失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取系统健康状态
    /// </summary>
    /// <returns>系统健康状态</returns>
    [HttpGet("/api/emqx/monitor/health")]
    [DisplayName("获取MQTT系统健康状态")]
    public Task<SystemHealthDto> GetSystemHealthAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT系统健康状态");
            
            var health = new SystemHealthDto
            {
                Status = "Healthy",
                Timestamp = DateTime.Now,
                Services = new List<ServiceHealthDto>
                {
                    new()
                    {
                        Name = "MQTT Broker",
                        Status = "Running",
                        ResponseTime = 15,
                        LastCheck = DateTime.Now
                    },
                    new()
                    {
                        Name = "Database",
                        Status = "Running",
                        ResponseTime = 8,
                        LastCheck = DateTime.Now
                    },
                    new()
                    {
                        Name = "Redis Cache",
                        Status = "Running",
                        ResponseTime = 3,
                        LastCheck = DateTime.Now
                    }
                },
                Metrics = new SystemMetricsDto
                {
                    CpuUsage = 45.2m,
                    MemoryUsage = 68.5m,
                    DiskUsage = 32.1m,
                    NetworkLatency = 12.5m
                }
            };
            
            _logger.LogInformation("获取MQTT系统健康状态成功");
            return Task.FromResult(health);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT系统健康状态失败");
            throw Oops.Oh("获取MQTT系统健康状态失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取客户端详细信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>客户端详细信息</returns>
    [HttpGet("/api/emqx/monitor/client-details")]
    [DisplayName("获取MQTT客户端详细信息")]
    public Task<ClientDetailsDto> GetClientDetailsAsync(string clientId)
    {
        try
        {
            _logger.LogInformation("获取MQTT客户端详细信息，ClientId: {ClientId}", clientId);
            
            var details = new ClientDetailsDto
            {
                ClientId = clientId,
                Status = MqttConnectionStatusEnum.Connected,
                ConnectedAt = DateTime.Now.AddHours(-2),
                LastActivity = DateTime.Now.AddMinutes(-5),
                IpAddress = "*************",
                UserAgent = "MQTTnet Client",
                Protocol = "MQTT 3.1.1",
                KeepAlive = 60,
                CleanSession = true,
                MessagesSent = 1250,
                MessagesReceived = 890,
                BytesSent = 125000,
                BytesReceived = 89000,
                Subscriptions = new List<string>
                {
                    "device/+/status",
                    "device/001/data",
                    "system/message"
                },
                LastWill = new LastWillDto
                {
                    Topic = "device/001/offline",
                    Message = "Device offline",
                    QoS = MqttQosLevelEnum.AtLeastOnce,
                    Retain = true
                }
            };
            
            _logger.LogInformation("获取MQTT客户端详细信息成功");
            return Task.FromResult(details);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT客户端详细信息失败，ClientId: {ClientId}", clientId);
            throw Oops.Oh("获取MQTT客户端详细信息失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取主题详细统计
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>主题详细统计</returns>
    [HttpGet("/api/emqx/monitor/topic-details")]
    [DisplayName("获取MQTT主题详细统计")]
    public Task<TopicDetailsDto> GetTopicDetailsAsync(string topic)
    {
        try
        {
            _logger.LogInformation("获取MQTT主题详细统计，Topic: {Topic}", topic);
            
            var details = new TopicDetailsDto
            {
                Topic = topic,
                SubscriberCount = 15,
                MessageCount = 2580,
                LastMessageTime = DateTime.Now.AddMinutes(-2),
                AverageMessageSize = 256,
                MessageFrequency = 45.2m,
                TopPublishers = new List<TopPublisherDto>
                {
                    new() { ClientId = "device001", MessageCount = 1200, Percentage = 46.5m },
                    new() { ClientId = "device002", MessageCount = 890, Percentage = 34.5m },
                    new() { ClientId = "system", MessageCount = 490, Percentage = 19.0m }
                },
                MessageHistory = GenerateMessageHistory(topic)
            };
            
            _logger.LogInformation("获取MQTT主题详细统计成功");
            return Task.FromResult(details);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT主题详细统计失败，Topic: {Topic}", topic);
            throw Oops.Oh("获取MQTT主题详细统计失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 生成消息历史数据
    /// </summary>
    private static List<MessageHistoryDto> GenerateMessageHistory(string topic)
    {
        var history = new List<MessageHistoryDto>();
        var random = new Random();
        var now = DateTime.Now;
        
        for (int i = 23; i >= 0; i--)
        {
            history.Add(new MessageHistoryDto
            {
                Hour = now.AddHours(-i),
                MessageCount = random.Next(50, 200),
                AverageSize = random.Next(100, 500)
            });
        }
        
        return history;
    }
}

/// <summary>
/// 导出报告输入DTO
/// </summary>
public class ExportReportInput
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
}

/// <summary>
/// 系统健康状态DTO
/// </summary>
public class SystemHealthDto
{
    public string Status { get; set; }
    public DateTime Timestamp { get; set; }
    public List<ServiceHealthDto> Services { get; set; }
    public SystemMetricsDto Metrics { get; set; }
}

/// <summary>
/// 服务健康状态DTO
/// </summary>
public class ServiceHealthDto
{
    public string Name { get; set; }
    public string Status { get; set; }
    public long ResponseTime { get; set; }
    public DateTime LastCheck { get; set; }
}

/// <summary>
/// 系统指标DTO
/// </summary>
public class SystemMetricsDto
{
    public decimal CpuUsage { get; set; }
    public decimal MemoryUsage { get; set; }
    public decimal DiskUsage { get; set; }
    public decimal NetworkLatency { get; set; }
}

/// <summary>
/// 客户端详细信息DTO
/// </summary>
public class ClientDetailsDto
{
    public string ClientId { get; set; }
    public MqttConnectionStatusEnum Status { get; set; }
    public DateTime ConnectedAt { get; set; }
    public DateTime LastActivity { get; set; }
    public string IpAddress { get; set; }
    public string UserAgent { get; set; }
    public string Protocol { get; set; }
    public int KeepAlive { get; set; }
    public bool CleanSession { get; set; }
    public long MessagesSent { get; set; }
    public long MessagesReceived { get; set; }
    public long BytesSent { get; set; }
    public long BytesReceived { get; set; }
    public List<string> Subscriptions { get; set; }
    public LastWillDto LastWill { get; set; }
}

/// <summary>
/// 遗嘱消息DTO
/// </summary>
public class LastWillDto
{
    public string Topic { get; set; }
    public string Message { get; set; }
    public MqttQosLevelEnum QoS { get; set; }
    public bool Retain { get; set; }
}

/// <summary>
/// 主题详细信息DTO
/// </summary>
public class TopicDetailsDto
{
    public string Topic { get; set; }
    public int SubscriberCount { get; set; }
    public long MessageCount { get; set; }
    public DateTime LastMessageTime { get; set; }
    public long AverageMessageSize { get; set; }
    public decimal MessageFrequency { get; set; }
    public List<TopPublisherDto> TopPublishers { get; set; }
    public List<MessageHistoryDto> MessageHistory { get; set; }
}

/// <summary>
/// 顶级发布者DTO
/// </summary>
public class TopPublisherDto
{
    public string ClientId { get; set; }
    public long MessageCount { get; set; }
    public decimal Percentage { get; set; }
}

/// <summary>
/// 消息历史DTO
/// </summary>
public class MessageHistoryDto
{
    public DateTime Hour { get; set; }
    public long MessageCount { get; set; }
    public long AverageSize { get; set; }
}