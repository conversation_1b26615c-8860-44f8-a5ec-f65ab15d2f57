// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Entity;
using Admin.NET.Core;
using Admin.NET.Core.Service;
using Admin.NET.Plugin.GreenDisplay.Service;
using Admin.NET.Plugin.GreenDisplay.Service.Dto;
using Microsoft.Extensions.Logging;

namespace Admin.NET.Application.Service.Cache;

/// <summary>
/// AP和设备缓存服务
/// </summary>
public class APDeviceCacheService : ISingleton
{
    private readonly SysCacheService _sysCacheService;
    private readonly ILogger<APDeviceCacheService> _logger;

    // 缓存过期时间配置
    private static readonly TimeSpan DefaultCacheExpiry = TimeSpan.FromMinutes(5); // 默认5分钟
    private static readonly TimeSpan ThirdPartyCacheExpiry = TimeSpan.FromSeconds(10); // 第三方数据10秒
    private static readonly TimeSpan StatusCacheExpiry = TimeSpan.FromSeconds(10); // 状态数据10秒

    public APDeviceCacheService(SysCacheService sysCacheService, ILogger<APDeviceCacheService> logger)
    {
        _sysCacheService = sysCacheService;
        _logger = logger;
    }

    #region AP缓存操作

    /// <summary>
    /// 获取所有AP数据（优先从缓存获取）
    /// </summary>
    /// <returns>AP数据列表</returns>
    public List<AccessPoints>? GetAllAccessPoints()
    {
        try
        {
            var cacheKey = $"{CacheConst.KeyAccessPoints}all";
            return _sysCacheService.Get<List<AccessPoints>>(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从缓存获取AP数据失败");
            return null;
        }
    }

    /// <summary>
    /// 设置所有AP数据到缓存
    /// </summary>
    /// <param name="accessPoints">AP数据列表</param>
    public void SetAllAccessPoints(List<AccessPoints> accessPoints)
    {
        try
        {
            var cacheKey = $"{CacheConst.KeyAccessPoints}all";
            _sysCacheService.Set(cacheKey, accessPoints, DefaultCacheExpiry);
            
            // 同时按MAC地址建立索引缓存
            foreach (var ap in accessPoints.Where(ap => !string.IsNullOrEmpty(ap.mac_address)))
            {
                var macKey = $"{CacheConst.KeyAccessPoints}mac:{ap.mac_address}";
                _sysCacheService.Set(macKey, ap, DefaultCacheExpiry);
            }
            
            _logger.LogDebug("已缓存 {Count} 个AP数据", accessPoints.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置AP数据到缓存失败");
        }
    }

    /// <summary>
    /// 根据MAC地址获取AP数据
    /// </summary>
    /// <param name="macAddress">MAC地址</param>
    /// <returns>AP数据</returns>
    public AccessPoints? GetAccessPointByMac(string macAddress)
    {
        if (string.IsNullOrEmpty(macAddress)) return null;
        
        try
        {
            var cacheKey = $"{CacheConst.KeyAccessPoints}mac:{macAddress}";
            return _sysCacheService.Get<AccessPoints>(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从缓存获取AP数据失败，MAC: {Mac}", macAddress);
            return null;
        }
    }

    /// <summary>
    /// 更新AP状态缓存
    /// </summary>
    /// <param name="updatedAPs">更新的AP列表</param>
    public void UpdateAccessPointsStatus(List<AccessPoints> updatedAPs)
    {
        try
        {
            foreach (var ap in updatedAPs.Where(ap => !string.IsNullOrEmpty(ap.mac_address)))
            {
                // 更新MAC索引缓存
                var macKey = $"{CacheConst.KeyAccessPoints}mac:{ap.mac_address}";
                _sysCacheService.Set(macKey, ap, DefaultCacheExpiry);
                
                // 更新状态缓存
                var statusKey = $"{CacheConst.KeyAPStatus}{ap.mac_address}";
                var statusInfo = new
                {
                    MacAddress = ap.mac_address,
                    Status = ap.ap_status,
                    IpAddress = ap.ip_address,
                    FirmwareVersion = ap.firmware_version,
                    UpdateTime = DateTime.Now
                };
                _sysCacheService.Set(statusKey, statusInfo, StatusCacheExpiry);
            }
            
            // 清除全量缓存，强制下次重新加载
            _sysCacheService.Remove($"{CacheConst.KeyAccessPoints}all");
            
            _logger.LogDebug("已更新 {Count} 个AP状态缓存", updatedAPs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新AP状态缓存失败");
        }
    }

    #endregion

    #region 设备缓存操作

    /// <summary>
    /// 获取所有设备数据（优先从缓存获取）
    /// </summary>
    /// <returns>设备数据列表</returns>
    public List<Devices>? GetAllDevices()
    {
        try
        {
            var cacheKey = $"{CacheConst.KeyDevices}all";
            return _sysCacheService.Get<List<Devices>>(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从缓存获取设备数据失败");
            return null;
        }
    }

    /// <summary>
    /// 设置所有设备数据到缓存
    /// </summary>
    /// <param name="devices">设备数据列表</param>
    public void SetAllDevices(List<Devices> devices)
    {
        try
        {
            var cacheKey = $"{CacheConst.KeyDevices}all";
            _sysCacheService.Set(cacheKey, devices, DefaultCacheExpiry);
            
            // 同时按MAC地址建立索引缓存
            foreach (var device in devices.Where(d => !string.IsNullOrEmpty(d.mac_address)))
            {
                var macKey = $"{CacheConst.KeyDevices}mac:{device.mac_address}";
                _sysCacheService.Set(macKey, device, DefaultCacheExpiry);
            }
            
            _logger.LogDebug("已缓存 {Count} 个设备数据", devices.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置设备数据到缓存失败");
        }
    }

    /// <summary>
    /// 根据MAC地址获取设备数据
    /// </summary>
    /// <param name="macAddress">MAC地址</param>
    /// <returns>设备数据</returns>
    public Devices? GetDeviceByMac(string macAddress)
    {
        if (string.IsNullOrEmpty(macAddress)) return null;
        
        try
        {
            var cacheKey = $"{CacheConst.KeyDevices}mac:{macAddress}";
            return _sysCacheService.Get<Devices>(cacheKey);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从缓存获取设备数据失败，MAC: {Mac}", macAddress);
            return null;
        }
    }

    /// <summary>
    /// 更新设备状态缓存
    /// </summary>
    /// <param name="updatedDevices">更新的设备列表</param>
    public void UpdateDevicesStatus(List<Devices> updatedDevices)
    {
        try
        {
            foreach (var device in updatedDevices.Where(d => !string.IsNullOrEmpty(d.mac_address)))
            {
                // 更新MAC索引缓存
                var macKey = $"{CacheConst.KeyDevices}mac:{device.mac_address}";
                _sysCacheService.Set(macKey, device, DefaultCacheExpiry);
                
                // 更新状态缓存
                var statusKey = $"{CacheConst.KeyDeviceStatus}{device.mac_address}";
                var statusInfo = new
                {
                    MacAddress = device.mac_address,
                    Status = device.status,
                    BatteryLevel = device.battery_level,
                    SignalStrength = device.signal_strength,
                    UpdateTime = DateTime.Now
                };
                _sysCacheService.Set(statusKey, statusInfo, StatusCacheExpiry);
            }
            
            // 清除全量缓存，强制下次重新加载
            _sysCacheService.Remove($"{CacheConst.KeyDevices}all");
            
            _logger.LogDebug("已更新 {Count} 个设备状态缓存", updatedDevices.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新设备状态缓存失败");
        }
    }

    #endregion

    #region 第三方平台数据缓存

    /// <summary>
    /// 获取第三方平台AP数据缓存
    /// </summary>
    /// <returns>第三方AP数据列表</returns>
    public List<APOutput>? GetThirdPartyAPs()
    {
        try
        {
            return _sysCacheService.Get<List<APOutput>>(CacheConst.KeyThirdPartyAPs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从缓存获取第三方AP数据失败");
            return null;
        }
    }

    /// <summary>
    /// 设置第三方平台AP数据缓存
    /// </summary>
    /// <param name="thirdPartyAPs">第三方AP数据列表</param>
    public void SetThirdPartyAPs(List<APOutput> thirdPartyAPs)
    {
        try
        {
            _sysCacheService.Set(CacheConst.KeyThirdPartyAPs, thirdPartyAPs, ThirdPartyCacheExpiry);
            _logger.LogDebug("已缓存 {Count} 个第三方AP数据", thirdPartyAPs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置第三方AP数据到缓存失败");
        }
    }

    /// <summary>
    /// 获取第三方平台设备数据缓存
    /// </summary>
    /// <returns>第三方设备数据列表</returns>
    public List<DeviceInfo>? GetThirdPartyDevices()
    {
        try
        {
            return _sysCacheService.Get<List<DeviceInfo>>(CacheConst.KeyThirdPartyDevices);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "从缓存获取第三方设备数据失败");
            return null;
        }
    }

    /// <summary>
    /// 设置第三方平台设备数据缓存
    /// </summary>
    /// <param name="thirdPartyDevices">第三方设备数据列表</param>
    public void SetThirdPartyDevices(List<DeviceInfo> thirdPartyDevices)
    {
        try
        {
            _sysCacheService.Set(CacheConst.KeyThirdPartyDevices, thirdPartyDevices, ThirdPartyCacheExpiry);
            _logger.LogDebug("已缓存 {Count} 个第三方设备数据", thirdPartyDevices.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设置第三方设备数据到缓存失败");
        }
    }

    #endregion

    #region 缓存清理操作

    /// <summary>
    /// 清除所有AP相关缓存
    /// </summary>
    public void ClearAllAPCache()
    {
        try
        {
            // 清除全量缓存
            _sysCacheService.Remove($"{CacheConst.KeyAccessPoints}all");
            
            // 清除第三方数据缓存
            _sysCacheService.Remove(CacheConst.KeyThirdPartyAPs);
            
            _logger.LogDebug("已清除所有AP缓存");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除AP缓存失败");
        }
    }

    /// <summary>
    /// 清除所有设备相关缓存
    /// </summary>
    public void ClearAllDeviceCache()
    {
        try
        {
            // 清除全量缓存
            _sysCacheService.Remove($"{CacheConst.KeyDevices}all");
            
            // 清除第三方数据缓存
            _sysCacheService.Remove(CacheConst.KeyThirdPartyDevices);
            
            _logger.LogDebug("已清除所有设备缓存");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清除设备缓存失败");
        }
    }

    #endregion
}
