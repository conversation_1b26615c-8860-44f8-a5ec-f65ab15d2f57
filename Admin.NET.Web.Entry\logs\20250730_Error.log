fail: 2025-07-30 10:07:23.9290424 +08:00 Wednesday L System.Logging.StringLogging[0] #47
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-30 10:07:23——错误SQL】
      
      [Sql]:UPDATE  `devices` S    INNER JOIN             (
                    
       SELECT 701080659771461 AS `ap_id`,@device_id_1 AS `device_id`,@device_name_2 AS `device_name`,@mac_address_3 AS `mac_address`,7 AS `device_type`,0 AS `status`,100 AS `battery_level`,-49 AS `signal_strength`,NULL AS `firmware_version`,NULL AS `BindTenantId`,NULL AS `BindUserId`,0 AS `IsDelete`,'2025-07-30 10:07:04.306' AS `UpdateTime`,NULL AS `UpdateUserId`,NULL AS `UpdateUserName`,702482185711685 AS `Id`
      
      
                  ) T ON S.`Id`=T.`Id`
                         SET S.`ap_id`=T.`ap_id`,S.`device_id`=T.`device_id`,S.`device_name`=T.`device_name`,S.`mac_address`=T.`mac_address`,S.`device_type`=T.`device_type`,S.`status`=T.`status`,S.`battery_level`=T.`battery_level`,S.`signal_strength`=T.`signal_strength`,S.`firmware_version`=T.`firmware_version`,S.`BindTenantId`=T.`BindTenantId`,S.`BindUserId`=T.`BindUserId`,S.`IsDelete`=T.`IsDelete`,S.`UpdateTime`=T.`UpdateTime`,S.`UpdateUserId`=T.`UpdateUserId`,S.`UpdateUserName`=T.`UpdateUserName` ; 
      [Pars]:
      [Name]:@ap_id [Value]:701080659771461 [Type]:Int64    
      [Name]:@device_id [Value]:46:C1:01:01:22:87 [Type]:String    
      [Name]:@device_name [Value]:设备_46:C1:01:01:22:87 [Type]:String    
      [Name]:@mac_address [Value]:46:C1:01:01:22:87 [Type]:String    
      [Name]:@device_type [Value]:7 [Type]:Int32    
      [Name]:@status [Value]:0 [Type]:Int32    
      [Name]:@battery_level [Value]:100 [Type]:Int32    
      [Name]:@signal_strength [Value]:-49 [Type]:Int32    
      [Name]:@firmware_version [Value]: [Type]:String    
      [Name]:@BindTenantId [Value]: [Type]:Int64    
      [Name]:@BindUserId [Value]: [Type]:Int64    
      [Name]:@IsDelete [Value]:False [Type]:Boolean    
      [Name]:@UpdateTime [Value]:2025-7-30 10:07:04 [Type]:DateTime    
      [Name]:@UpdateUserId [Value]: [Type]:Int64    
      [Name]:@UpdateUserName [Value]: [Type]:String    
      [Name]:@Id [Value]:702482185711685 [Type]:Int64    
      [Name]:@device_id_1 [Value]:46:C1:01:01:22:87 [Type]:String    
      [Name]:@device_name_2 [Value]:设备_46:C1:01:01:22:87 [Type]:String    
      [Name]:@mac_address_3 [Value]:46:C1:01:01:22:87 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Failed to read the result set.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-30 11:22:59.5713985 +08:00 Wednesday L System.Logging.StringLogging[0] #34
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-30 11:22:58——错误SQL】
      
      [Sql]:INSERT INTO `SysLogOp`  
                 (`HttpMethod`,`RequestUrl`,`RequestParam`,`ReturnResult`,`EventId`,`ThreadId`,`TraceId`,`Exception`,`Message`,`ControllerName`,`ActionName`,`DisplayTitle`,`Status`,`RemoteIp`,`Location`,`Longitude`,`Latitude`,`Browser`,`Os`,`Elapsed`,`LogDateTime`,`LogLevel`,`Account`,`RealName`,`TenantId`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`)
           VALUES
                 (@HttpMethod,@RequestUrl,@RequestParam,@ReturnResult,@EventId,@ThreadId,@TraceId,@Exception,@Message,@ControllerName,@ActionName,@DisplayTitle,@Status,@RemoteIp,@Location,@Longitude,@Latitude,@Browser,@Os,@Elapsed,@LogDateTime,@LogLevel,@Account,@RealName,@TenantId,@CreateTime,@UpdateTime,@CreateUserId,@CreateUserName,@UpdateUserId,@UpdateUserName,@Id) ; 
      [Pars]:
      [Name]:@HttpMethod [Value]:POST [Type]:String    
      [Name]:@RequestUrl [Value]:http://localhost:5005/api/devices/statisticsByMeetingRoom [Type]:String    
      [Name]:@RequestParam [Value]:{"startTime":null,"endTime":null,"deviceType":null,"apId":null,"meetingRoomId":"0"} [Type]:String    
      [Name]:@ReturnResult [Value]:{"type":"Admin.NET.Core.AdminResult<System.Object>","httpStatusCode":"200","actType":"System.Threading.Tasks.Task<System.Collections.Generic.List<Admin.NET.Application.MeetingRoomDeviceStatistics>>","value":{"code":"200","type":"success","message":"","result":[{"meetingRoomId":"0","meetingRoomName":"所有会议室","totalDevices":"1","onlineDevices":"1","offlineDevices":"0","lowBatteryDevices":"0","weakSignalDevices":"0","onlineRate":100.0}],"extras":null,"time":"2025-07-30 11:22:32"}} [Type]:String    
      [Name]:@EventId [Value]:0 [Type]:Int32    
      [Name]:@ThreadId [Value]:32 [Type]:Int32    
      [Name]:@TraceId [Value]:00-b9f6655e4980d06b34fdc7485915179e-1bffab2a9ee4bd33-00 [Type]:String    
      [Name]:@Exception [Value]: [Type]:String    
      [Name]:@Message [Value]:┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.DevicesService.GetDeviceStatisticsByMeetingRoom
      ┣ 
      ┣ 控制器名称：                           DevicesService
      ┣ 操作名称：                             GetDeviceStatisticsByMeetingRoom
      ┣ 显示名称：                             按会议室分组获取设备统计信息
      ┣ 路由信息：                             [area]: ; [controller]: devices; [action]: statisticsByMeetingRoom
      ┣ 请求方式：                             POST
      ┣ 请求地址：                             http://localhost:5005/api/devices/statisticsByMeetingRoom
      ┣ HTTP 协议：                            HTTP/1.1
      ┣ 来源地址：                             http://localhost:8888/
      ┣ 请求端源：                             client
      ┣ 浏览器标识：                           Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                       zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                       *******
      ┣ 客户端源端口：                         58623
      ┣ 服务端 IP 地址：                       *******
      ┣ 服务端源端口：                         5005
      ┣ 客户端连接 ID：                        00-b9f6655e4980d06b34fdc7485915179e-1bffab2a9ee4bd33-00
      ┣ 服务线程 ID：                          #59
      ┣ 执行耗时：                             111ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                               
      ┣ 响应端：                               
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                             Microsoft Windows 10.0.26100
      ┣ 系统架构：                             X64
      ┣ 基础框架：                             Furion.Pure v4.9.7.93
      ┣ .NET 架构：                            .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                         http://localhost:5005/
      ┣ 运行环境：                             Development
      ┣ 启动程序集：                           Admin.NET.Web.Entry
      ┣ 进程名称：                             iisexpress
      ┣ 托管程序：                             iisexpress
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                            Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.c9vc4dsQ4ylgwe-dXwxEIMiVmnww4ouBJpmpXPQ7d98
      ┣ 
      ┣ UserId (integer64)：                   *************
      ┣ TenantId (integer64)：                 *************
      ┣ Account (string)：                     superadmin
      ┣ RealName (string)：                    超级管理员
      ┣ AccountType (integer32)：              999
      ┣ OrgId (integer32)：                    0
      ┣ OrgName (JSON_NULL)：                  
      ┣ OrgType (JSON_NULL)：                  
      ┣ iat (integer64)：                      ********** (2025-07-30 09:57:36:0000(+08:00) Wednesday L)
      ┣ nbf (integer64)：                      ********** (2025-07-30 09:57:36:0000(+08:00) Wednesday L)
      ┣ exp (integer64)：                      ********** (2025-08-06 09:57:36:0000(+08:00) Wednesday L)
      ┣ iss (string)：                         Admin.NET
      ┣ aud (string)：                         Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                               application/json, text/plain, */*
      ┣ Accept-Encoding：                      gzip, deflate, br, zstd
      ┣ Accept-Language：                      zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                        Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.c9vc4dsQ4ylgwe-dXwxEIMiVmnww4ouBJpmpXPQ7d98
      ┣ Connection：                           keep-alive
      ┣ Content-Length：                       19
      ┣ Content-Type：                         application/json
      ┣ Host：                                 localhost:5005
      ┣ Referer：                              http://localhost:8888/
      ┣ User-Agent：                           Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ sec-ch-ua-platform：                   "Windows"
      ┣ sec-ch-ua：                            "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：                     ?0
      ┣ Origin：                               http://localhost:8888
      ┣ Sec-Fetch-Site：                       same-site
      ┣ Sec-Fetch-Mode：                       cors
      ┣ Sec-Fetch-Dest：                       empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                         application/json
      ┣ 
      ┣ input (DeviceStatisticsInput)：        {"startTime":null,"endTime":null,"deviceType":null,"apId":null,"meetingRoomId":0}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                       200
      ┣ 原始类型：                             System.Threading.Tasks.Task<System.Collections.Generic.List<Admin.NET.Application.MeetingRoomDeviceStatistics>>
      ┣ 最终类型：                             Admin.NET.Core.AdminResult<System.Object>
      ┣ 最终返回值：                           {"code":200,"type":"success","message":"","result":[{"meetingRoomId":0,"meetingRoomName":"所有会议室","totalDevices":1,"onlineDevices":1,"offlineDevices":0,"lowBatteryDevices":0,"weakSignalDevices":0,"onlineRate":100.0}],"extras":null,"time":"2025-07-30T11:22:32.4564752+08:00"}
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
       [Type]:String    
      [Name]:@ControllerName [Value]:devices [Type]:String    
      [Name]:@ActionName [Value]:GetDeviceStatisticsByMeetingRoom [Type]:String    
      [Name]:@DisplayTitle [Value]:按会议室分组获取设备统计信息 [Type]:String    
      [Name]:@Status [Value]:200 [Type]:String    
      [Name]:@RemoteIp [Value]:******* [Type]:String    
      [Name]:@Location [Value]: [Type]:String    
      [Name]:@Longitude [Value]:0 [Type]:Double    
      [Name]:@Latitude [Value]:0 [Type]:Double    
      [Name]:@Browser [Value]:Chrome 138.0 / Other [Type]:String    
      [Name]:@Os [Value]:Windows 10  [Type]:String    
      [Name]:@Elapsed [Value]:111 [Type]:Int64    
      [Name]:@LogDateTime [Value]:2025-7-30 11:22:32 [Type]:DateTime    
      [Name]:@LogLevel [Value]:2 [Type]:Int64    
      [Name]:@Account [Value]:superadmin [Type]:String    
      [Name]:@RealName [Value]:超级管理员 [Type]:String    
      [Name]:@TenantId [Value]:************* [Type]:Int64    
      [Name]:@CreateTime [Value]:2025-7-30 11:22:32 [Type]:DateTime    
      [Name]:@UpdateTime [Value]: [Type]:DateTime    
      [Name]:@CreateUserId [Value]:************* [Type]:Int64    
      [Name]:@CreateUserName [Value]: [Type]:String    
      [Name]:@UpdateUserId [Value]: [Type]:Int64    
      [Name]:@UpdateUserName [Value]: [Type]:String    
      [Name]:@Id [Value]:*************** [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Failed to read the result set.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-30 11:22:59.6176767 +08:00 Wednesday L System.Logging.StringLogging[0] #28 '00-4f3218ce33fe5e5f1981f7b291f6320a-2b53e9d7a50d5dcc-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-30 11:22:58——错误SQL】
      
      [Sql]:SELECT Count(*) FROM `devices` `d` Left JOIN `device_bindings` `db` ON (( `d`.`Id` = `db`.`device_id` ) AND NOT ( `db`.`IsDelete`=1 ) ) AND ( `db`.`IsDelete` = @IsDelete0 )  Left JOIN `access_points` `ap` ON (( `d`.`ap_id` = `ap`.`Id` ) AND NOT ( `ap`.`IsDelete`=1 ) ) AND ( `ap`.`IsDelete` = @IsDelete0 )   WHERE NOT ( `d`.`IsDelete`=1 )   AND ( `d`.`IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      [Name]:@Const1 [Value]:NULL [Type]:String    
      [Name]:@MethodConst2 [Value]: [Type]:AnsiString    
      [Name]:@MethodConst3 [Value]:1 [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Failed to read the result set.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-30 11:22:59.6150544 +08:00 Wednesday L System.Logging.StringLogging[0] #32 '00-8d550b6eac49a0d7fd70122451125b8a-eb78309425910631-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-30 11:22:58——错误SQL】
      
      [Sql]:SELECT Count(*) FROM `devices` `d` Left JOIN `device_bindings` `db` ON (( `d`.`Id` = `db`.`device_id` ) AND NOT ( `db`.`IsDelete`=1 ) ) AND ( `db`.`IsDelete` = @IsDelete0 )  Left JOIN `access_points` `ap` ON (( `d`.`ap_id` = `ap`.`Id` ) AND NOT ( `ap`.`IsDelete`=1 ) ) AND ( `ap`.`IsDelete` = @IsDelete0 )   WHERE NOT ( `d`.`IsDelete`=1 )   AND ( `d`.`IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      [Name]:@Const1 [Value]:NULL [Type]:String    
      [Name]:@MethodConst2 [Value]: [Type]:AnsiString    
      [Name]:@MethodConst3 [Value]:1 [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Failed to read the result set.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-30 11:23:00.3048264 +08:00 Wednesday L Admin.NET.Core.DatabaseLoggingWriter[0] #34
      [Admin.NET.Core.dll] async Task Admin.NET.Core.DatabaseLoggingWriter.WriteAsync(LogMessage logMsg, bool flush)
      操作日志入库      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource<System.Int32>.GetResult(Int16 token)
         at MySqlConnector.Protocol.Serialization.SocketByteHandler.DoReadBytesAsync(Memory`1 buffer) in /_/src/MySqlConnector/Protocol/Serialization/SocketByteHandler.cs:line 89
         at MySqlConnector.Protocol.Serialization.BufferedByteReader.ReadBytesAsync(IByteHandler byteHandler, ArraySegment`1 buffer, Int32 totalBytesToRead, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/BufferedByteReader.cs:line 34
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<ReadPacketAsync>g__AddContinuation|1_0(ValueTask`1 headerBytes, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 423
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<DoReadPayloadAsync>g__AddContinuation|5_0(ValueTask`1 readPacketTask, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ArraySegmentHolder`1 previousPayloads, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 494
         at MySqlConnector.Core.ServerSession.ReceiveReplyAsyncAwaited(ValueTask`1 task) in /_/src/MySqlConnector/Core/ServerSession.cs:line 951
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 43
         at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
         at Admin.NET.Core.DatabaseLoggingWriter.WriteAsync(LogMessage logMsg, Boolean flush) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Logging\DatabaseLoggingWriter.cs:line 159
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-30 11:23:00.7705701 +08:00 Wednesday L System.Logging.LoggingMonitor[0] #28 '00-4f3218ce33fe5e5f1981f7b291f6320a-2b53e9d7a50d5dcc-00'
      [Furion.Pure.dll] async Task LoggingMonitorAttribute.MonitorAsync(MethodInfo actionMethod, IDictionary<string, object> parameterValues, FilterContext context, dynamic next)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.DevicesService.Page
      ┣ 
      ┣ 控制器名称：                      DevicesService
      ┣ 操作名称：                        Page
      ┣ 显示名称：                        分页查询蓝牙桌牌设备
      ┣ 路由信息：                        [area]: ; [controller]: devices; [action]: page
      ┣ 请求方式：                        POST
      ┣ 请求地址：                        http://localhost:5005/api/devices/page
      ┣ HTTP 协议：                       HTTP/1.1
      ┣ 来源地址：                        http://localhost:8888/
      ┣ 请求端源：                        client
      ┣ 浏览器标识：                      Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                  zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                  *******
      ┣ 客户端源端口：                    58623
      ┣ 服务端 IP 地址：                  *******
      ┣ 服务端源端口：                    5005
      ┣ 客户端连接 ID：                   00-4f3218ce33fe5e5f1981f7b291f6320a-2b53e9d7a50d5dcc-00
      ┣ 服务线程 ID：                     #42
      ┣ 执行耗时：                        27641ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                          
      ┣ 响应端：                          
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                        Microsoft Windows 10.0.26100
      ┣ 系统架构：                        X64
      ┣ 基础框架：                        Furion.Pure v4.9.7.93
      ┣ .NET 架构：                       .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                    http://localhost:5005/
      ┣ 运行环境：                        Development
      ┣ 启动程序集：                      Admin.NET.Web.Entry
      ┣ 进程名称：                        iisexpress
      ┣ 托管程序：                        iisexpress
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                       Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.c9vc4dsQ4ylgwe-dXwxEIMiVmnww4ouBJpmpXPQ7d98
      ┣ 
      ┣ UserId (integer64)：              *************
      ┣ TenantId (integer64)：            *************
      ┣ Account (string)：                superadmin
      ┣ RealName (string)：               超级管理员
      ┣ AccountType (integer32)：         999
      ┣ OrgId (integer32)：               0
      ┣ OrgName (JSON_NULL)：             
      ┣ OrgType (JSON_NULL)：             
      ┣ iat (integer64)：                 ********** (2025-07-30 09:57:36:0000(+08:00) Wednesday L)
      ┣ nbf (integer64)：                 ********** (2025-07-30 09:57:36:0000(+08:00) Wednesday L)
      ┣ exp (integer64)：                 ********** (2025-08-06 09:57:36:0000(+08:00) Wednesday L)
      ┣ iss (string)：                    Admin.NET
      ┣ aud (string)：                    Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                          application/json, text/plain, */*
      ┣ Accept-Encoding：                 gzip, deflate, br, zstd
      ┣ Accept-Language：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                   Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.c9vc4dsQ4ylgwe-dXwxEIMiVmnww4ouBJpmpXPQ7d98
      ┣ Connection：                      keep-alive
      ┣ Content-Length：                  99
      ┣ Content-Type：                    application/json
      ┣ Host：                            localhost:5005
      ┣ Referer：                         http://localhost:8888/
      ┣ User-Agent：                      Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ sec-ch-ua-platform：              "Windows"
      ┣ sec-ch-ua：                       "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：                ?0
      ┣ Origin：                          http://localhost:8888
      ┣ Sec-Fetch-Site：                  same-site
      ┣ Sec-Fetch-Mode：                  cors
      ┣ Sec-Fetch-Dest：                  empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                    application/json
      ┣ 
      ┣ input (PageDevicesInput)：        {"device_name":null,"device_type":null,"status":null,"meeting_room_id":null,"page":1,"pageSize":20,"field":"createTime","order":"descending","descStr":"descending","search":null,"keyword":null,"filter":null}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                  200
      ┣ 原始类型：                        System.Threading.Tasks.Task<Admin.NET.Core.SqlSugarPagedList<Admin.NET.Application.DevicesOutput>>
      ┣ 最终类型：                        
      ┣ 最终返回值：                      null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                            MySqlConnector.MySqlException
      ┣ 消息：                            Failed to read the result set.
      ┣ 错误堆栈：                        at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetCountAsync()
         at SqlSugar.QueryableProvider`1.CountAsync()
         at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)
         at Admin.NET.Core.SqlSugarPagedExtensions.ToPagedListAsync[TEntity](ISugarQueryable`1 query, Int32 pageIndex, Int32 pageSize) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarPagedList.cs:line 112
         at Admin.NET.Application.DevicesService.Page(PageDevicesInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Devices\DevicesService.cs:line 106
         at lambda_method464(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource<System.Int32>.GetResult(Int16 token)
         at MySqlConnector.Protocol.Serialization.SocketByteHandler.DoReadBytesAsync(Memory`1 buffer) in /_/src/MySqlConnector/Protocol/Serialization/SocketByteHandler.cs:line 89
         at MySqlConnector.Protocol.Serialization.BufferedByteReader.ReadBytesAsync(IByteHandler byteHandler, ArraySegment`1 buffer, Int32 totalBytesToRead, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/BufferedByteReader.cs:line 34
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<ReadPacketAsync>g__AddContinuation|1_0(ValueTask`1 headerBytes, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 423
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<DoReadPayloadAsync>g__AddContinuation|5_0(ValueTask`1 readPacketTask, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ArraySegmentHolder`1 previousPayloads, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 494
         at MySqlConnector.Core.ServerSession.ReceiveReplyAsyncAwaited(ValueTask`1 task) in /_/src/MySqlConnector/Core/ServerSession.cs:line 951
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 43
         at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetCountAsync()
         at SqlSugar.QueryableProvider`1.CountAsync()
         at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)
         at Admin.NET.Core.SqlSugarPagedExtensions.ToPagedListAsync[TEntity](ISugarQueryable`1 query, Int32 pageIndex, Int32 pageSize) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarPagedList.cs:line 112
         at Admin.NET.Application.DevicesService.Page(PageDevicesInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Devices\DevicesService.cs:line 106
         at lambda_method464(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-30 11:23:00.7742098 +08:00 Wednesday L System.Logging.LoggingMonitor[0] #32 '00-8d550b6eac49a0d7fd70122451125b8a-eb78309425910631-00'
      [Furion.Pure.dll] async Task LoggingMonitorAttribute.MonitorAsync(MethodInfo actionMethod, IDictionary<string, object> parameterValues, FilterContext context, dynamic next)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.DevicesService.Page
      ┣ 
      ┣ 控制器名称：                      DevicesService
      ┣ 操作名称：                        Page
      ┣ 显示名称：                        分页查询蓝牙桌牌设备
      ┣ 路由信息：                        [area]: ; [controller]: devices; [action]: page
      ┣ 请求方式：                        POST
      ┣ 请求地址：                        http://localhost:5005/api/devices/page
      ┣ HTTP 协议：                       HTTP/1.1
      ┣ 来源地址：                        http://localhost:8888/
      ┣ 请求端源：                        client
      ┣ 浏览器标识：                      Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                  zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                  *******
      ┣ 客户端源端口：                    57011
      ┣ 服务端 IP 地址：                  *******
      ┣ 服务端源端口：                    5005
      ┣ 客户端连接 ID：                   00-8d550b6eac49a0d7fd70122451125b8a-eb78309425910631-00
      ┣ 服务线程 ID：                     #59
      ┣ 执行耗时：                        29092ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                          
      ┣ 响应端：                          
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                        Microsoft Windows 10.0.26100
      ┣ 系统架构：                        X64
      ┣ 基础框架：                        Furion.Pure v4.9.7.93
      ┣ .NET 架构：                       .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                    http://localhost:5005/
      ┣ 运行环境：                        Development
      ┣ 启动程序集：                      Admin.NET.Web.Entry
      ┣ 进程名称：                        iisexpress
      ┣ 托管程序：                        iisexpress
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                       Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.c9vc4dsQ4ylgwe-dXwxEIMiVmnww4ouBJpmpXPQ7d98
      ┣ 
      ┣ UserId (integer64)：              *************
      ┣ TenantId (integer64)：            *************
      ┣ Account (string)：                superadmin
      ┣ RealName (string)：               超级管理员
      ┣ AccountType (integer32)：         999
      ┣ OrgId (integer32)：               0
      ┣ OrgName (JSON_NULL)：             
      ┣ OrgType (JSON_NULL)：             
      ┣ iat (integer64)：                 ********** (2025-07-30 09:57:36:0000(+08:00) Wednesday L)
      ┣ nbf (integer64)：                 ********** (2025-07-30 09:57:36:0000(+08:00) Wednesday L)
      ┣ exp (integer64)：                 ********** (2025-08-06 09:57:36:0000(+08:00) Wednesday L)
      ┣ iss (string)：                    Admin.NET
      ┣ aud (string)：                    Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                          application/json, text/plain, */*
      ┣ Accept-Encoding：                 gzip, deflate, br, zstd
      ┣ Accept-Language：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                   Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.c9vc4dsQ4ylgwe-dXwxEIMiVmnww4ouBJpmpXPQ7d98
      ┣ Connection：                      keep-alive
      ┣ Content-Length：                  99
      ┣ Content-Type：                    application/json
      ┣ Host：                            localhost:5005
      ┣ Referer：                         http://localhost:8888/
      ┣ User-Agent：                      Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ sec-ch-ua-platform：              "Windows"
      ┣ sec-ch-ua：                       "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：                ?0
      ┣ Origin：                          http://localhost:8888
      ┣ Sec-Fetch-Site：                  same-site
      ┣ Sec-Fetch-Mode：                  cors
      ┣ Sec-Fetch-Dest：                  empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                    application/json
      ┣ 
      ┣ input (PageDevicesInput)：        {"device_name":null,"device_type":null,"status":null,"meeting_room_id":null,"page":1,"pageSize":20,"field":"createTime","order":"descending","descStr":"descending","search":null,"keyword":null,"filter":null}
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                  200
      ┣ 原始类型：                        System.Threading.Tasks.Task<Admin.NET.Core.SqlSugarPagedList<Admin.NET.Application.DevicesOutput>>
      ┣ 最终类型：                        
      ┣ 最终返回值：                      null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                            MySqlConnector.MySqlException
      ┣ 消息：                            Failed to read the result set.
      ┣ 错误堆栈：                        at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetCountAsync()
         at SqlSugar.QueryableProvider`1.CountAsync()
         at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)
         at Admin.NET.Core.SqlSugarPagedExtensions.ToPagedListAsync[TEntity](ISugarQueryable`1 query, Int32 pageIndex, Int32 pageSize) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarPagedList.cs:line 112
         at Admin.NET.Application.DevicesService.Page(PageDevicesInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Devices\DevicesService.cs:line 106
         at lambda_method464(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource<System.Int32>.GetResult(Int16 token)
         at MySqlConnector.Protocol.Serialization.SocketByteHandler.DoReadBytesAsync(Memory`1 buffer) in /_/src/MySqlConnector/Protocol/Serialization/SocketByteHandler.cs:line 89
         at MySqlConnector.Protocol.Serialization.BufferedByteReader.ReadBytesAsync(IByteHandler byteHandler, ArraySegment`1 buffer, Int32 totalBytesToRead, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/BufferedByteReader.cs:line 34
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<ReadPacketAsync>g__AddContinuation|1_0(ValueTask`1 headerBytes, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 423
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<DoReadPayloadAsync>g__AddContinuation|5_0(ValueTask`1 readPacketTask, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ArraySegmentHolder`1 previousPayloads, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 494
         at MySqlConnector.Core.ServerSession.ReceiveReplyAsyncAwaited(ValueTask`1 task) in /_/src/MySqlConnector/Core/ServerSession.cs:line 951
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 43
         at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.MySqlProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetCountAsync()
         at SqlSugar.QueryableProvider`1.CountAsync()
         at SqlSugar.QueryableProvider`1.ToPageListAsync(Int32 pageIndex, Int32 pageSize, RefAsync`1 totalNumber)
         at Admin.NET.Core.SqlSugarPagedExtensions.ToPagedListAsync[TEntity](ISugarQueryable`1 query, Int32 pageIndex, Int32 pageSize) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarPagedList.cs:line 112
         at Admin.NET.Application.DevicesService.Page(PageDevicesInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Devices\DevicesService.cs:line 106
         at lambda_method464(Closure, Object)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-30 11:23:18.6778766 +08:00 Wednesday L System.Logging.StringLogging[0] #28 '00-46001e48dd5ded9e8efb51ce5be265c2-667c1f9331ecd5e3-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-30 11:23:18——错误SQL】
      
      [Sql]:SELECT `room_id`,`room_name`,`room_code`,`room_location`,`capacity`,`equipment`,`meeting_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `meeting_rooms`  WHERE NOT ( `IsDelete`=1 )   AND ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Failed to read the result set.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-30 11:23:18.8227653 +08:00 Wednesday L Admin.NET.Application.DevicesService[0] #28 '00-46001e48dd5ded9e8efb51ce5be265c2-667c1f9331ecd5e3-00'
      [Admin.NET.Application.dll] async Task<List<MeetingRoomDeviceStatistics>> Admin.NET.Application.DevicesService.GetDeviceStatisticsByMeetingRoom(DeviceStatisticsInput input)
      按会议室分组获取设备统计信息时发生错误，查询条件：Admin.NET.Application.DeviceStatisticsInput      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)
         at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource<System.Int32>.GetResult(Int16 token)
         at MySqlConnector.Protocol.Serialization.SocketByteHandler.DoReadBytesAsync(Memory`1 buffer) in /_/src/MySqlConnector/Protocol/Serialization/SocketByteHandler.cs:line 89
         at MySqlConnector.Protocol.Serialization.BufferedByteReader.ReadBytesAsync(IByteHandler byteHandler, ArraySegment`1 buffer, Int32 totalBytesToRead, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/BufferedByteReader.cs:line 34
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<ReadPacketAsync>g__AddContinuation|1_0(ValueTask`1 headerBytes, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 423
         at MySqlConnector.Protocol.Serialization.ProtocolUtility.<DoReadPayloadAsync>g__AddContinuation|5_0(ValueTask`1 readPacketTask, BufferedByteReader bufferedByteReader, IByteHandler byteHandler, Func`1 getNextSequenceNumber, ArraySegmentHolder`1 previousPayloads, ProtocolErrorBehavior protocolErrorBehavior, IOBehavior ioBehavior) in /_/src/MySqlConnector/Protocol/Serialization/ProtocolUtility.cs:line 494
         at MySqlConnector.Core.ServerSession.ReceiveReplyAsyncAwaited(ValueTask`1 task) in /_/src/MySqlConnector/Core/ServerSession.cs:line 951
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 43
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.DevicesService.GetDeviceStatisticsByMeetingRoom(DeviceStatisticsInput input) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\Devices\DevicesService.cs:line 870
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
