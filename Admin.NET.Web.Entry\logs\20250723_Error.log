crit: 2025-07-23 10:31:59.0973495 +08:00 Wednesday L System.Logging.ScheduleService[0] #21
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Schedule hosted service is stopped.
crit: 2025-07-23 10:35:06.5130123 +08:00 Wednesday L System.Logging.ScheduleService[0] #24
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Schedule hosted service is stopped.
fail: 2025-07-23 10:53:19.8612818 +08:00 Wednesday L Admin.NET.Application.MeetingStaffService[0] #5 '00-1b3f1141d35dfffac3faedb9531c9802-45038ef44e497a9c-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      导入数据事务失败: 发现与第三方平台重复的员工编号: 20250205, 20250206，请修改后重新导入
fail: 2025-07-23 10:53:26.9505618 +08:00 Wednesday L Admin.NET.Application.MeetingStaffService[0] #28 '00-65d2b299f155ba8c5761047c52e80226-d759aba5d0dc06f9-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      导入数据事务失败: 发现与第三方平台重复的员工编号: 20250205, 20250206，请修改后重新导入
fail: 2025-07-23 10:54:35.6430031 +08:00 Wednesday L Admin.NET.Application.MeetingStaffService[0] #3 '00-e749df7c96ee7b3ee0cd4b89f7d06271-bfa8381ea62769c1-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      导入数据事务失败: 发现与第三方平台重复的员工编号: 20250205, 20250206，请修改后重新导入
fail: 2025-07-23 10:55:21.9160461 +08:00 Wednesday L Admin.NET.Application.MeetingStaffService[0] #30 '00-94468943b895d32f2c294bad8e7cc33f-efd6160ba65966c0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, string message, params object[] args)
      导入数据事务失败: 发现与第三方平台重复的员工编号: 20250205, 20250206，请修改后重新导入
fail: 2025-07-23 15:05:21.6111150 +08:00 Wednesday L System.Logging.StringLogging[0] #55 '00-a2dbcf5508bf230035e22d346a4cb6db-f27b06bcfb9a8b93-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-23 15:05:21——错误SQL】
      
      [Sql]:SELECT `ConnectionId`,`UserId`,`UserName`,`RealName`,`Time`,`Ip`,`Browser`,`Os`,`TenantId`,`Id` FROM `SysOnlineUser`   WHERE ( `ConnectionId` = @ConnectionId0 )   LIMIT 0,1 
      [Pars]:
      [Name]:@ConnectionId0 [Value]:Cij7UqJg7u6Ypl_dMsjdUw [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Couldn't connect to server
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-23 15:05:24.0278906 +08:00 Wednesday L System.Logging.StringLogging[0] #25 '00-2ac580ac400af99b9cc58e430876820e-9ed0a9597fdca647-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-23 15:05:21——错误SQL】
      
      [Sql]:SELECT `ConnectionId`,`UserId`,`UserName`,`RealName`,`Time`,`Ip`,`Browser`,`Os`,`TenantId`,`Id` FROM `SysOnlineUser`   WHERE ( `ConnectionId` = @ConnectionId0 )   LIMIT 0,1 
      [Pars]:
      [Name]:@ConnectionId0 [Value]:gN3hsZBOM-OhG7iEDoF1xg [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Couldn't connect to server
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-23 15:05:24.5296743 +08:00 Wednesday L System.Logging.StringLogging[0] #24 '00-4dc2e3d5bb70df01b3a5d70a1cd2ee32-0c280a24d3ee58a0-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-23 15:05:24——错误SQL】
      
      [Sql]:INSERT INTO `SysOnlineUser`  
                 (`ConnectionId`,`UserId`,`UserName`,`RealName`,`Time`,`Ip`,`Browser`,`Os`,`TenantId`,`Id`)
           VALUES
                 (@ConnectionId,@UserId,@UserName,@RealName,@Time,@Ip,@Browser,@Os,@TenantId,@Id) ; 
      [Pars]:
      [Name]:@ConnectionId [Value]:6RYlFUKYp0Yo_ZYOWJujyw [Type]:String    
      [Name]:@UserId [Value]:1300000000101 [Type]:Int64    
      [Name]:@UserName [Value]:superadmin [Type]:String    
      [Name]:@RealName [Value]:超级管理员 [Type]:String    
      [Name]:@Time [Value]:2025-7-23 15:05:19 [Type]:DateTime    
      [Name]:@Ip [Value]:127.0.0.1 [Type]:String    
      [Name]:@Browser [Value]:Chrome 138.0 / Other [Type]:String    
      [Name]:@Os [Value]:Windows 10  [Type]:String    
      [Name]:@TenantId [Value]:1300000000001 [Type]:Int64    
      [Name]:@Id [Value]:700781024346181 [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Couldn't connect to server
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-23 15:05:24.5279271 +08:00 Wednesday L Microsoft.AspNetCore.SignalR.HubConnectionHandler[1] #25 '00-2ac580ac400af99b9cc58e430876820e-9ed0a9597fdca647-00'
      [Microsoft.AspNetCore.SignalR.Core.dll] async Task Microsoft.AspNetCore.SignalR.HubConnectionHandler<THub>.HubOnDisconnectedAsync(HubConnectionContext connection, Exception exception)
      Error when dispatching 'OnDisconnectedAsync' on hub.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Couldn't connect to server
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at SqlSugar.QueryableProvider`1.FirstAsync()
         at SqlSugar.QueryableProvider`1.FirstAsync(Expression`1 expression)
         at Admin.NET.Core.OnlineUserHub.OnDisconnectedAsync(Exception exception) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Hub\OnlineUserHub.cs:line 103
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnDisconnectedAsync(HubConnectionContext connection, Exception exception)
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnDisconnectedAsync(HubConnectionContext connection, Exception exception)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.HubOnDisconnectedAsync(HubConnectionContext connection, Exception exception)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-23 15:05:24.5358514 +08:00 Wednesday L Microsoft.AspNetCore.SignalR.HubConnectionHandler[1] #55 '00-a2dbcf5508bf230035e22d346a4cb6db-f27b06bcfb9a8b93-00'
      [Microsoft.AspNetCore.SignalR.Core.dll] async Task Microsoft.AspNetCore.SignalR.HubConnectionHandler<THub>.HubOnDisconnectedAsync(HubConnectionContext connection, Exception exception)
      Error when dispatching 'OnDisconnectedAsync' on hub.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Couldn't connect to server
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at SqlSugar.QueryableProvider`1.FirstAsync()
         at SqlSugar.QueryableProvider`1.FirstAsync(Expression`1 expression)
         at Admin.NET.Core.OnlineUserHub.OnDisconnectedAsync(Exception exception) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Hub\OnlineUserHub.cs:line 103
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnDisconnectedAsync(HubConnectionContext connection, Exception exception)
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnDisconnectedAsync(HubConnectionContext connection, Exception exception)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.HubOnDisconnectedAsync(HubConnectionContext connection, Exception exception)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-23 15:05:25.6390261 +08:00 Wednesday L Microsoft.AspNetCore.SignalR.HubConnectionHandler[1] #24 '00-4dc2e3d5bb70df01b3a5d70a1cd2ee32-0c280a24d3ee58a0-00'
      [Microsoft.AspNetCore.SignalR.Core.dll] async Task Microsoft.AspNetCore.SignalR.HubConnectionHandler<THub>.RunHubAsync(HubConnectionContext connection)
      Error when dispatching 'OnConnectedAsync' on hub.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Couldn't connect to server
         at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
         at SqlSugar.SimpleClient`1.InsertAsync(T insertObj)
         at Admin.NET.Core.OnlineUserHub.OnConnectedAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Hub\OnlineUserHub.cs:line 64
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnConnectedAsync(HubConnectionContext connection)
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnConnectedAsync(HubConnectionContext connection)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.RunHubAsync(HubConnectionContext connection)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-23 15:05:26.0675401 +08:00 Wednesday L Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionManager[3] #55 '00-a2dbcf5508bf230035e22d346a4cb6db-f27b06bcfb9a8b93-00'
      [Microsoft.AspNetCore.Http.Connections.dll] async Task Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionManager.DisposeAndRemoveAsync(HttpConnectionContext connection, bool closeGracefully, HttpConnectionStopStatus status)
      Failed disposing connection Cij7UqJg7u6Ypl_dMsjdUw.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Couldn't connect to server
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at SqlSugar.QueryableProvider`1.FirstAsync()
         at SqlSugar.QueryableProvider`1.FirstAsync(Expression`1 expression)
         at Admin.NET.Core.OnlineUserHub.OnDisconnectedAsync(Exception exception) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Hub\OnlineUserHub.cs:line 103
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnDisconnectedAsync(HubConnectionContext connection, Exception exception)
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnDisconnectedAsync(HubConnectionContext connection, Exception exception)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.HubOnDisconnectedAsync(HubConnectionContext connection, Exception exception)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.RunHubAsync(HubConnectionContext connection)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.OnConnectedAsync(ConnectionContext connection)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.OnConnectedAsync(ConnectionContext connection)
         at Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionContext.ExecuteApplication(ConnectionDelegate connectionDelegate)
         at Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionContext.WaitOnTasks(Task applicationTask, Task transportTask, Boolean closeGracefully)
         at Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionContext.DisposeAsync(Boolean closeGracefully)
         at Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionManager.DisposeAndRemoveAsync(HttpConnectionContext connection, Boolean closeGracefully, HttpConnectionStopStatus status)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-23 15:05:26.0758686 +08:00 Wednesday L Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionManager[3] #25 '00-2ac580ac400af99b9cc58e430876820e-9ed0a9597fdca647-00'
      [Microsoft.AspNetCore.Http.Connections.dll] async Task Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionManager.DisposeAndRemoveAsync(HttpConnectionContext connection, bool closeGracefully, HttpConnectionStopStatus status)
      Failed disposing connection gN3hsZBOM-OhG7iEDoF1xg.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Couldn't connect to server
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at SqlSugar.QueryableProvider`1.FirstAsync()
         at SqlSugar.QueryableProvider`1.FirstAsync(Expression`1 expression)
         at Admin.NET.Core.OnlineUserHub.OnDisconnectedAsync(Exception exception) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Hub\OnlineUserHub.cs:line 103
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnDisconnectedAsync(HubConnectionContext connection, Exception exception)
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnDisconnectedAsync(HubConnectionContext connection, Exception exception)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.HubOnDisconnectedAsync(HubConnectionContext connection, Exception exception)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.RunHubAsync(HubConnectionContext connection)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.OnConnectedAsync(ConnectionContext connection)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.OnConnectedAsync(ConnectionContext connection)
         at Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionContext.ExecuteApplication(ConnectionDelegate connectionDelegate)
         at Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionContext.WaitOnTasks(Task applicationTask, Task transportTask, Boolean closeGracefully)
         at Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionContext.DisposeAsync(Boolean closeGracefully)
         at Microsoft.AspNetCore.Http.Connections.Internal.HttpConnectionManager.DisposeAndRemoveAsync(HttpConnectionContext connection, Boolean closeGracefully, HttpConnectionStopStatus status)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-23 15:05:26.5238885 +08:00 Wednesday L System.Logging.StringLogging[0] #25 '00-0f3cb52a4326ff8ed5a53e0762628504-81d81d2472b0715a-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-23 15:05:26——错误SQL】
      
      [Sql]:INSERT INTO `SysOnlineUser`  
                 (`ConnectionId`,`UserId`,`UserName`,`RealName`,`Time`,`Ip`,`Browser`,`Os`,`TenantId`,`Id`)
           VALUES
                 (@ConnectionId,@UserId,@UserName,@RealName,@Time,@Ip,@Browser,@Os,@TenantId,@Id) ; 
      [Pars]:
      [Name]:@ConnectionId [Value]:p3sqmF20lVh_kxapAQ62-Q [Type]:String    
      [Name]:@UserId [Value]:1300000000101 [Type]:Int64    
      [Name]:@UserName [Value]:superadmin [Type]:String    
      [Name]:@RealName [Value]:超级管理员 [Type]:String    
      [Name]:@Time [Value]:2025-7-23 15:05:21 [Type]:DateTime    
      [Name]:@Ip [Value]:127.0.0.1 [Type]:String    
      [Name]:@Browser [Value]:Chrome 138.0 / Other [Type]:String    
      [Name]:@Os [Value]:Windows 10  [Type]:String    
      [Name]:@TenantId [Value]:1300000000001 [Type]:Int64    
      [Name]:@Id [Value]:700781033132101 [Type]:Int64    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Couldn't connect to server
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-23 15:05:26.7387320 +08:00 Wednesday L Microsoft.AspNetCore.SignalR.HubConnectionHandler[1] #25 '00-0f3cb52a4326ff8ed5a53e0762628504-81d81d2472b0715a-00'
      [Microsoft.AspNetCore.SignalR.Core.dll] async Task Microsoft.AspNetCore.SignalR.HubConnectionHandler<THub>.RunHubAsync(HubConnectionContext connection)
      Error when dispatching 'OnConnectedAsync' on hub.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Couldn't connect to server
         at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.MySqlProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
         at SqlSugar.SimpleClient`1.InsertAsync(T insertObj)
         at Admin.NET.Core.OnlineUserHub.OnConnectedAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Hub\OnlineUserHub.cs:line 64
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnConnectedAsync(HubConnectionContext connection)
         at Microsoft.AspNetCore.SignalR.Internal.DefaultHubDispatcher`1.OnConnectedAsync(HubConnectionContext connection)
         at Microsoft.AspNetCore.SignalR.HubConnectionHandler`1.RunHubAsync(HubConnectionContext connection)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-23 16:26:31.1862439 +08:00 Wednesday L Admin.NET.Application.AccessPointsService[0] #25 '00-1db37945d14237eef9214e14a8379a2a-02065a72d7f9e793-00'
      [Admin.NET.Application.dll] async Task Admin.NET.Application.AccessPointsService.Delete(DeleteAccessPointsInput input)+(?) => { }
      第三方平台删除AP失败，MAC地址: 66:C6:DB:B1:50:22
fail: 2025-07-23 16:33:49.8844270 +08:00 Wednesday L Admin.NET.Application.AccessPointsService[0] #54 '00-72f47ee122d8eb60e4c9a04921287db1-a37d46e842655ec0-00'
      [Admin.NET.Application.dll] async Task Admin.NET.Application.AccessPointsService.Delete(DeleteAccessPointsInput input)+(?) => { }
      第三方平台删除AP失败，MAC地址: 66:C6:DB:B1:50:22
fail: 2025-07-23 17:06:57.1598718 +08:00 Wednesday L Admin.NET.Application.AccessPointsService[0] #26 '00-bf277f6e948cd9b9e3c05f8f03697ec5-0361ceee2a061c8a-00'
      [Admin.NET.Application.dll] async Task<int> Admin.NET.Application.AccessPointsService.BatchDelete(List<DeleteAccessPointsInput> input)+(?) => { }
      第三方平台删除AP失败，MAC地址: 66:C6:DB:B1:50:22
fail: 2025-07-23 17:07:00.6362616 +08:00 Wednesday L Admin.NET.Application.AccessPointsService[0] #58 '00-bf277f6e948cd9b9e3c05f8f03697ec5-0361ceee2a061c8a-00'
      [Admin.NET.Application.dll] async Task<int> Admin.NET.Application.AccessPointsService.BatchDelete(List<DeleteAccessPointsInput> input)+(?) => { }
      第三方平台删除AP失败，MAC地址: D4:AD:20:B8:97:99
fail: 2025-07-23 17:07:00.9710621 +08:00 Wednesday L Admin.NET.Application.AccessPointsService[0] #58 '00-bf277f6e948cd9b9e3c05f8f03697ec5-0361ceee2a061c8a-00'
      [Admin.NET.Application.dll] async Task<int> Admin.NET.Application.AccessPointsService.BatchDelete(List<DeleteAccessPointsInput> input)+(?) => { }
      部分第三方平台AP删除失败，失败数量: 2, 失败MAC地址: 66:C6:DB:B1:50:22, D4:AD:20:B8:97:99
