﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <ImplicitUsings>enable</ImplicitUsings>
    <SatelliteResourceLanguages>zh-<PERSON></SatelliteResourceLanguages>
    <PublishReadyToRunComposite>true</PublishReadyToRunComposite>
    <ProduceReferenceAssembly>false</ProduceReferenceAssembly>
    <UserSecretsId>ad9369d1-f29b-4f8f-a7df-8b4d7aa0726b</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <GenerateSatelliteAssembliesForCore>true</GenerateSatelliteAssembliesForCore>
    <Copyright>Admin.NET</Copyright>
    <Description>Admin.NET 通用权限开发平台</Description>
    <AssemblyVersion>1.0.0</AssemblyVersion>
    <FileVersion>1.0.0</FileVersion>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="logs\**" />
    <Compile Remove="publish\**" />
    <EmbeddedResource Remove="logs\**" />
    <EmbeddedResource Remove="publish\**" />
    <None Remove="logs\**" />
    <None Remove="publish\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\avatar\**" CopyToPublishDirectory="Never" />
    <Content Update="wwwroot\upload\**" CopyToPublishDirectory="Never" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\template\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="GeoLite2-City.mmdb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="ip2region.db">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="UpdateScripts\1.0.0.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="sensitive-words.txt">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
	
  <ItemGroup>
    <Content Update="wwwroot\upload\logo.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Admin.NET.Web.Core\Admin.NET.Web.Core.csproj" />
    <ProjectReference Include="..\Plugins\Admin.NET.Plugin.EMQX\Admin.NET.Plugin.EMQX.csproj" />
  </ItemGroup>

</Project>
