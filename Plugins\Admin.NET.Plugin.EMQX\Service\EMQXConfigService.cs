// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// EMQX配置管理服务
/// </summary>
[ApiDescriptionSettings("EMQX", Name = "MQTT配置管理", Order = 101)]
public class EMQXConfigService : IDynamicApiController, ITransient
{
    private readonly EMQXOptions _options;
    private readonly IConfiguration _configuration;

    public EMQXConfigService(IOptions<EMQXOptions> options, IConfiguration configuration)
    {
        _options = options.Value;
        _configuration = configuration;
    }

    /// <summary>
    /// 获取MQTT配置
    /// </summary>
    /// <returns>配置信息</returns>
    [HttpGet("/api/emqx/config")]
    public Task<EMQXOptions> GetConfigAsync()
    {
        return Task.FromResult(_options);
    }

    /// <summary>
    /// 获取服务器配置
    /// </summary>
    /// <returns>服务器配置</returns>
    [HttpGet("/api/emqx/config/server")]
    public Task<ServerConfigInput> GetServerConfigAsync()
    {
        var config = new ServerConfigInput
        {
            Server = _options.Server,
            Port = _options.Port,
            UseTLS = _options.UseTLS,
            Username = _options.Username,
            Password = _options.Password,
            KeepAlivePeriod = _options.KeepAlivePeriod,
            ConnectionTimeout = _options.ConnectionTimeout,
            RetryInterval = _options.RetryInterval,
            MaxRetryAttempts = _options.MaxRetryAttempts
        };
        return Task.FromResult(config);
    }

    /// <summary>
    /// 获取阿里云配置
    /// </summary>
    /// <returns>阿里云配置</returns>
    [HttpGet("/api/emqx/config/aliyun")]
    public Task<AliyunConfigInput> GetAliyunConfigAsync()
    {
        var config = new AliyunConfigInput
        {
            Enabled = _options.AliyunMqtt.Enabled,
            InstanceId = _options.AliyunMqtt.InstanceId,
            AccessKeyId = _options.AliyunMqtt.AccessKeyId,
            AccessKeySecret = _options.AliyunMqtt.AccessKeySecret,
            GroupId = _options.AliyunMqtt.GroupId,
            Region = _options.AliyunMqtt.Region
        };
        return Task.FromResult(config);
    }

    /// <summary>
    /// 测试连接
    /// </summary>
    /// <param name="input">服务器配置</param>
    /// <returns>测试结果</returns>
    [HttpPost("/api/emqx/config/test")]
    public Task<ConfigValidationResult> TestConnectionAsync(ServerConfigInput input)
    {
        return ValidateConfigAsync(input);
    }

    /// <summary>
    /// 更新MQTT服务器配置
    /// </summary>
    /// <param name="input">服务器配置</param>
    /// <returns>更新结果</returns>
    [HttpPost("/api/emqx/config/server")]
    public async Task<bool> UpdateServerConfigAsync(ServerConfigInput input)
    {
        try
        {
            // 这里应该更新配置文件或数据库
            // 由于Admin.NET的配置机制，这里只是示例
            _options.Server = input.Server;
            _options.Port = input.Port;
            _options.UseTLS = input.UseTLS;
            _options.Username = input.Username;
            _options.Password = input.Password;
            _options.KeepAlivePeriod = input.KeepAlivePeriod;
            _options.ConnectionTimeout = input.ConnectionTimeout;
            _options.RetryInterval = input.RetryInterval;
            _options.MaxRetryAttempts = input.MaxRetryAttempts;
            
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"更新服务器配置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新阿里云MQTT配置
    /// </summary>
    /// <param name="input">阿里云配置</param>
    /// <returns>更新结果</returns>
    [HttpPost("/api/emqx/config/aliyun")]
    public async Task<bool> UpdateAliyunConfigAsync(AliyunConfigInput input)
    {
        try
        {
            _options.AliyunMqtt.Enabled = input.Enabled;
            _options.AliyunMqtt.InstanceId = input.InstanceId;
            _options.AliyunMqtt.AccessKeyId = input.AccessKeyId;
            _options.AliyunMqtt.AccessKeySecret = input.AccessKeySecret;
            _options.AliyunMqtt.GroupId = input.GroupId;
            _options.AliyunMqtt.Region = input.Region;
            
            return await Task.FromResult(true);
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"更新阿里云配置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 验证服务器连接
    /// </summary>
    /// <param name="input">服务器配置</param>
    /// <returns>验证结果</returns>
    [HttpPost("/api/emqx/config/validate")]
    public async Task<ConfigValidationResult> ValidateConfigAsync(ServerConfigInput input)
    {
        var result = new ConfigValidationResult
        {
            IsValid = false,
            Message = "连接失败",
            TestTime = DateTime.Now
        };

        try
        {
            var factory = new MqttFactory();
            using var client = factory.CreateMqttClient();
            
            var options = new MqttClientOptionsBuilder()
                .WithTcpServer(input.Server, input.Port)
                .WithClientId($"config_test_{Guid.NewGuid():N}")
                .WithCleanSession(true)
                .WithKeepAlivePeriod(TimeSpan.FromSeconds(30));
                
            if (!string.IsNullOrEmpty(input.Username))
            {
                options.WithCredentials(input.Username, input.Password);
            }
            
            if (input.UseTLS)
            {
                options.WithTls();
            }
            
            var connectResult = await client.ConnectAsync(options.Build());
            if (connectResult.ResultCode == MqttClientConnectResultCode.Success)
            {
                result.IsValid = true;
                result.Message = "连接成功";
                result.ServerInfo = $"服务器版本: {connectResult.ServerReference ?? "未知"}";
                
                await client.DisconnectAsync();
            }
            else
            {
                result.Message = $"连接失败: {connectResult.ResultCode} - {connectResult.ReasonString}";
            }
        }
        catch (Exception ex)
        {
            result.Message = $"连接异常: {ex.Message}";
        }
        
        return result;
    }

    /// <summary>
    /// 生成阿里云MQTT认证信息
    /// </summary>
    /// <param name="input">认证参数</param>
    /// <returns>认证信息</returns>
    [HttpPost("/api/emqx/config/aliyun/auth")]
    public Task<AliyunAuthResult> GenerateAliyunAuthAsync(AliyunAuthInput input)
    {
        try
        {
            var clientId = AliyunMqttSignatureUtil.GenerateClientId(input.GroupId, input.ClientId);
            var username = AliyunMqttSignatureUtil.GenerateUsername(input.AccessKeyId, input.InstanceId);
            var password = AliyunMqttSignatureUtil.GeneratePassword(input.AccessKeySecret, clientId);
            
            var result = new AliyunAuthResult
            {
                ClientId = clientId,
                Username = username,
                Password = password,
                GenerateTime = DateTime.Now
            };
            
            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"生成阿里云认证信息失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取主题模板
    /// </summary>
    /// <returns>主题模板列表</returns>
    [HttpGet("/api/emqx/config/topics")]
    public Task<List<TopicTemplateDto>> GetTopicTemplatesAsync()
    {
        var templates = new List<TopicTemplateDto>
        {
            new() { Name = "设备状态", Topic = EMQXConst.DEVICE_STATUS_TOPIC, Description = "设备在线状态上报", MessageType = MqttMessageTypeEnum.DeviceStatus },
            new() { Name = "设备数据", Topic = EMQXConst.DEVICE_DATA_TOPIC, Description = "设备数据上报", MessageType = MqttMessageTypeEnum.DeviceData },
            new() { Name = "设备控制", Topic = EMQXConst.DEVICE_CONTROL_TOPIC, Description = "设备控制指令", MessageType = MqttMessageTypeEnum.DeviceControl },
            new() { Name = "系统消息", Topic = "system/message", Description = "系统广播消息", MessageType = MqttMessageTypeEnum.SystemMessage },
            new() { Name = "自定义消息", Topic = "custom/{clientId}", Description = "自定义消息主题", MessageType = MqttMessageTypeEnum.CustomMessage }
        };
        
        return Task.FromResult(templates);
    }
}

/// <summary>
/// 服务器配置输入DTO
/// </summary>
public class ServerConfigInput
{
    /// <summary>
    /// 服务器地址
    /// </summary>
    [Required, Description("服务器地址")]
    public string Server { get; set; }

    /// <summary>
    /// 端口
    /// </summary>
    [Required, Description("端口")]
    public int Port { get; set; } = 1883;

    /// <summary>
    /// 是否使用TLS
    /// </summary>
    [Description("是否使用TLS")]
    public bool UseTLS { get; set; } = false;

    /// <summary>
    /// 用户名
    /// </summary>
    [Description("用户名")]
    public string Username { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    [Description("密码")]
    public string Password { get; set; }

    /// <summary>
    /// 保持连接时间(秒)
    /// </summary>
    [Description("保持连接时间(秒)")]
    public int KeepAlivePeriod { get; set; } = 60;

    /// <summary>
    /// 连接超时时间(秒)
    /// </summary>
    [Description("连接超时时间(秒)")]
    public int ConnectionTimeout { get; set; } = 30;

    /// <summary>
    /// 重试间隔(秒)
    /// </summary>
    [Description("重试间隔(秒)")]
    public int RetryInterval { get; set; } = 5;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    [Description("最大重试次数")]
    public int MaxRetryAttempts { get; set; } = 3;
}

/// <summary>
/// 阿里云配置输入DTO
/// </summary>
public class AliyunConfigInput
{
    /// <summary>
    /// 是否启用
    /// </summary>
    [Description("是否启用")]
    public bool Enabled { get; set; } = false;

    /// <summary>
    /// 实例ID
    /// </summary>
    [Description("实例ID")]
    public string InstanceId { get; set; }

    /// <summary>
    /// AccessKey ID
    /// </summary>
    [Description("AccessKey ID")]
    public string AccessKeyId { get; set; }

    /// <summary>
    /// AccessKey Secret
    /// </summary>
    [Description("AccessKey Secret")]
    public string AccessKeySecret { get; set; }

    /// <summary>
    /// 组ID
    /// </summary>
    [Description("组ID")]
    public string GroupId { get; set; } = "GID_DEFAULT";

    /// <summary>
    /// 地域
    /// </summary>
    [Description("地域")]
    public string Region { get; set; } = "cn-hangzhou";
}

/// <summary>
/// 配置验证结果
/// </summary>
public class ConfigValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 验证消息
    /// </summary>
    public string Message { get; set; }

    /// <summary>
    /// 服务器信息
    /// </summary>
    public string ServerInfo { get; set; }

    /// <summary>
    /// 测试时间
    /// </summary>
    public DateTime TestTime { get; set; }
}

/// <summary>
/// 阿里云认证输入DTO
/// </summary>
public class AliyunAuthInput
{
    /// <summary>
    /// 组ID
    /// </summary>
    [Required, Description("组ID")]
    public string GroupId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required, Description("客户端ID")]
    public string ClientId { get; set; }

    /// <summary>
    /// AccessKey ID
    /// </summary>
    [Required, Description("AccessKey ID")]
    public string AccessKeyId { get; set; }

    /// <summary>
    /// AccessKey Secret
    /// </summary>
    [Required, Description("AccessKey Secret")]
    public string AccessKeySecret { get; set; }

    /// <summary>
    /// 实例ID
    /// </summary>
    [Required, Description("实例ID")]
    public string InstanceId { get; set; }
}

/// <summary>
/// 阿里云认证结果
/// </summary>
public class AliyunAuthResult
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; }

    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GenerateTime { get; set; }
}

/// <summary>
/// 主题模板
/// </summary>
public class TopicTemplate
{
    /// <summary>
    /// 模板名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public MqttMessageTypeEnum MessageType { get; set; }
}

/// <summary>
/// 主题模板DTO
/// </summary>
public class TopicTemplateDto
{
    /// <summary>
    /// 模板名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public MqttMessageTypeEnum MessageType { get; set; }
}
