// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// MQTT连接信息DTO
/// </summary>
public class MqttConnectionDto
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 组ID
    /// </summary>
    public string GroupId { get; set; }

    /// <summary>
    /// 连接状态
    /// </summary>
    public MqttConnectionStatusEnum Status { get; set; }

    /// <summary>
    /// 连接时间
    /// </summary>
    public DateTime? ConnectedTime { get; set; }

    /// <summary>
    /// 最后活跃时间
    /// </summary>
    public DateTime? LastActiveTime { get; set; }

    /// <summary>
    /// 在线时长(秒)
    /// </summary>
    public long OnlineDuration { get; set; }

    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress { get; set; }

    /// <summary>
    /// 用户代理
    /// </summary>
    public string UserAgent { get; set; }

    /// <summary>
    /// 订阅主题数量
    /// </summary>
    public int SubscriptionCount { get; set; }

    /// <summary>
    /// 发送消息数量
    /// </summary>
    public long SentMessageCount { get; set; }

    /// <summary>
    /// 接收消息数量
    /// </summary>
    public long ReceivedMessageCount { get; set; }
}

/// <summary>
/// MQTT连接输入DTO
/// </summary>
public class MqttConnectionInput
{
    /// <summary>
    /// 组ID
    /// </summary>
    [Required, Description("组ID")]
    public string GroupId { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Required, Description("客户端ID")]
    public string ClientId { get; set; }

    /// <summary>
    /// 用户名(可选，用于自定义认证)
    /// </summary>
    [Description("用户名")]
    public string Username { get; set; }

    /// <summary>
    /// 密码(可选，用于自定义认证)
    /// </summary>
    [Description("密码")]
    public string Password { get; set; }

    /// <summary>
    /// 是否使用阿里云MQTT认证
    /// </summary>
    [Description("是否使用阿里云MQTT认证")]
    public bool UseAliyunAuth { get; set; } = false;

    /// <summary>
    /// 阿里云AccessKey ID(使用阿里云认证时必填)
    /// </summary>
    [Description("阿里云AccessKey ID")]
    public string AccessKeyId { get; set; }

    /// <summary>
    /// 阿里云实例ID(使用阿里云认证时必填)
    /// </summary>
    [Description("阿里云实例ID")]
    public string InstanceId { get; set; }

    /// <summary>
    /// 清理会话
    /// </summary>
    [Description("清理会话")]
    public bool CleanSession { get; set; } = true;

    /// <summary>
    /// 保持连接时间(秒)
    /// </summary>
    [Description("保持连接时间(秒)")]
    public int KeepAlivePeriod { get; set; } = 60;
}