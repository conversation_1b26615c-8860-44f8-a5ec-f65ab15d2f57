// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// EMQX配置控制器
/// </summary>
[ApiDescriptionSettings("EMQX", Name = "MQTT配置", Order = 101)]
public class EMQXConfigController : IDynamicApiController, ITransient
{
    private readonly EMQXConfigService _configService;
    private readonly ILogger<EMQXConfigController> _logger;

    public EMQXConfigController(EMQXConfigService configService, ILogger<EMQXConfigController> logger)
    {
        _configService = configService;
        _logger = logger;
    }

    /// <summary>
    /// 获取MQTT服务器配置
    /// </summary>
    /// <returns>服务器配置</returns>
    [HttpGet("/api/emqx/config/server")]
    [DisplayName("获取MQTT服务器配置")]
    public async Task<ServerConfigInput> GetServerConfigAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT服务器配置");
            var config = await _configService.GetServerConfigAsync();
            _logger.LogInformation("获取MQTT服务器配置成功");
            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT服务器配置失败");
            throw Oops.Oh("获取MQTT服务器配置失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 更新MQTT服务器配置
    /// </summary>
    /// <param name="config">服务器配置</param>
    /// <returns>更新结果</returns>
    [HttpPost("/api/emqx/config/server")]
    [DisplayName("更新MQTT服务器配置")]
    public async Task<bool> UpdateServerConfigAsync(ServerConfigInput config)
    {
        try
        {
            _logger.LogInformation("更新MQTT服务器配置，Server: {Server}:{Port}", config.Server, config.Port);
            var result = await _configService.UpdateServerConfigAsync(config);
            _logger.LogInformation("更新MQTT服务器配置结果: {Result}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新MQTT服务器配置失败");
            throw Oops.Oh("更新MQTT服务器配置失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取阿里云MQTT配置
    /// </summary>
    /// <returns>阿里云配置</returns>
    [HttpGet("/api/emqx/config/aliyun")]
    [DisplayName("获取阿里云MQTT配置")]
    public async Task<AliyunConfigInput> GetAliyunConfigAsync()
    {
        try
        {
            _logger.LogInformation("获取阿里云MQTT配置");
            var config = await _configService.GetAliyunConfigAsync();
            _logger.LogInformation("获取阿里云MQTT配置成功");
            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取阿里云MQTT配置失败");
            throw Oops.Oh("获取阿里云MQTT配置失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 更新阿里云MQTT配置
    /// </summary>
    /// <param name="config">阿里云配置</param>
    /// <returns>更新结果</returns>
    [HttpPost("/api/emqx/config/aliyun")]
    [DisplayName("更新阿里云MQTT配置")]
    public async Task<bool> UpdateAliyunConfigAsync(AliyunConfigInput config)
    {
        try
        {
            _logger.LogInformation("更新阿里云MQTT配置，InstanceId: {InstanceId}", config.InstanceId);
            var result = await _configService.UpdateAliyunConfigAsync(config);
            _logger.LogInformation("更新阿里云MQTT配置结果: {Result}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新阿里云MQTT配置失败");
            throw Oops.Oh("更新阿里云MQTT配置失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 测试服务器连接
    /// </summary>
    /// <param name="config">服务器配置</param>
    /// <returns>连接测试结果</returns>
    [HttpPost("/api/emqx/config/test-connection")]
    [DisplayName("测试MQTT服务器连接")]
    public async Task<ConnectionTestResultDto> TestConnectionAsync(ServerConfigInput config)
    {
        try
        {
            _logger.LogInformation("测试MQTT服务器连接，Server: {Server}:{Port}", config.Server, config.Port);
            var result = await _configService.TestConnectionAsync(config);
            _logger.LogInformation("MQTT服务器连接测试结果: {IsValid}", result.IsValid);
            return new ConnectionTestResultDto
            {
                Success = result.IsValid,
                ErrorMessage = result.Message,
                TestTime = result.TestTime,
                ServerVersion = result.ServerInfo
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "测试MQTT服务器连接失败");
            return new ConnectionTestResultDto
            {
                Success = false,
                ErrorMessage = ex.Message,
                TestTime = DateTime.Now
            };
        }
    }

    /// <summary>
    /// 生成阿里云认证信息
    /// </summary>
    /// <param name="input">生成参数</param>
    /// <returns>认证信息</returns>
    [HttpPost("/api/emqx/config/generate-aliyun-auth")]
    [DisplayName("生成阿里云MQTT认证信息")]
    public async Task<AliyunAuthInfoDto> GenerateAliyunAuthAsync(GenerateAliyunAuthInput input)
    {
        try
        {
            _logger.LogInformation("生成阿里云MQTT认证信息，GroupId: {GroupId}, ClientId: {ClientId}", input.GroupId, input.ClientId);
            
            // 转换为服务层需要的类型
            var authInput = new AliyunAuthInput
            {
                GroupId = input.GroupId,
                ClientId = input.ClientId,
                AccessKeyId = input.AccessKeyId,
                AccessKeySecret = input.AccessKeySecret,
                InstanceId = input.InstanceId
            };
            
            var authResult = await _configService.GenerateAliyunAuthAsync(authInput);
            
            // 转换为控制器返回的类型
            var authInfo = new AliyunAuthInfoDto
            {
                ClientId = authResult.ClientId,
                Username = authResult.Username,
                Password = authResult.Password,
                GenerateTime = authResult.GenerateTime,
                ExpiresIn = 3600 // 默认1小时过期
            };
            
            _logger.LogInformation("生成阿里云MQTT认证信息成功");
            return authInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成阿里云MQTT认证信息失败");
            throw Oops.Oh("生成阿里云MQTT认证信息失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取主题模板
    /// </summary>
    /// <returns>主题模板列表</returns>
    [HttpGet("/api/emqx/config/topic-templates")]
    [DisplayName("获取MQTT主题模板")]
    public async Task<List<TopicTemplateDto>> GetTopicTemplatesAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT主题模板");
            var templates = await _configService.GetTopicTemplatesAsync();
            _logger.LogInformation("获取到 {Count} 个MQTT主题模板", templates.Count);
            return templates;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT主题模板失败");
            throw Oops.Oh("获取MQTT主题模板失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 验证主题格式
    /// </summary>
    /// <param name="topic">主题</param>
    /// <returns>验证结果</returns>
    [HttpPost("/api/emqx/config/validate-topic")]
    [DisplayName("验证MQTT主题格式")]
    public Task<TopicValidationResultDto> ValidateTopicAsync(string topic)
    {
        try
        {
            _logger.LogInformation("验证MQTT主题格式，Topic: {Topic}", topic);
            
            var result = new TopicValidationResultDto
            {
                Topic = topic,
                IsValid = IsValidTopic(topic),
                ValidationTime = DateTime.Now
            };
            
            if (!result.IsValid)
            {
                result.ErrorMessage = "主题格式不正确，请检查主题名称";
            }
            
            _logger.LogInformation("MQTT主题格式验证结果: {IsValid}", result.IsValid);
            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证MQTT主题格式失败");
            return Task.FromResult(new TopicValidationResultDto
            {
                Topic = topic,
                IsValid = false,
                ErrorMessage = ex.Message,
                ValidationTime = DateTime.Now
            });
        }
    }

    /// <summary>
    /// 获取配置历史
    /// </summary>
    /// <returns>配置历史列表</returns>
    [HttpGet("/api/emqx/config/history")]
    [DisplayName("获取MQTT配置历史")]
    public Task<List<ConfigHistoryDto>> GetConfigHistoryAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT配置历史");
            
            // 模拟配置历史数据
            var history = new List<ConfigHistoryDto>
            {
                new()
                {
                    Id = Guid.NewGuid().ToString(),
                    ConfigType = "ServerConfig",
                    ChangeDescription = "更新服务器地址",
                    ChangeTime = DateTime.Now.AddHours(-2),
                    OperatorId = "admin",
                    OperatorName = "管理员",
                    OldValue = "{\"Server\":\"old.mqtt.server.com\",\"Port\":1883}",
                    NewValue = "{\"Server\":\"new.mqtt.server.com\",\"Port\":1883}"
                },
                new()
                {
                    Id = Guid.NewGuid().ToString(),
                    ConfigType = "AliyunConfig",
                    ChangeDescription = "更新阿里云实例ID",
                    ChangeTime = DateTime.Now.AddDays(-1),
                    OperatorId = "admin",
                    OperatorName = "管理员",
                    OldValue = "{\"InstanceId\":\"old-instance-id\"}",
                    NewValue = "{\"InstanceId\":\"new-instance-id\"}"
                }
            };
            
            _logger.LogInformation("获取到 {Count} 条MQTT配置历史", history.Count);
            return Task.FromResult(history);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT配置历史失败");
            throw Oops.Oh("获取MQTT配置历史失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 重置配置为默认值
    /// </summary>
    /// <param name="configType">配置类型</param>
    /// <returns>重置结果</returns>
    [HttpPost("/api/emqx/config/reset")]
    [DisplayName("重置MQTT配置")]
    public Task<bool> ResetConfigAsync(string configType)
    {
        try
        {
            _logger.LogInformation("重置MQTT配置，ConfigType: {ConfigType}", configType);
            
            // 模拟重置操作
            var success = configType switch
            {
                "ServerConfig" => true,
                "AliyunConfig" => true,
                _ => false
            };
            
            _logger.LogInformation("重置MQTT配置结果: {Success}", success);
            return Task.FromResult(success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置MQTT配置失败");
            throw Oops.Oh("重置MQTT配置失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 验证主题格式
    /// </summary>
    private static bool IsValidTopic(string topic)
    {
        if (string.IsNullOrWhiteSpace(topic))
            return false;
            
        // MQTT主题不能包含通配符在发布时
        if (topic.Contains('+') || topic.Contains('#'))
            return false;
            
        // 主题不能以/开头或结尾
        if (topic.StartsWith('/') || topic.EndsWith('/'))
            return false;
            
        // 主题长度限制
        if (topic.Length > 65535)
            return false;
            
        return true;
    }
}

/// <summary>
/// 连接测试结果DTO
/// </summary>
public class ConnectionTestResultDto
{
    public bool Success { get; set; }
    public string ErrorMessage { get; set; }
    public DateTime TestTime { get; set; }
    public long ResponseTime { get; set; }
    public string ServerVersion { get; set; }
}

/// <summary>
/// 生成阿里云认证输入DTO
/// </summary>
public class GenerateAliyunAuthInput
{
    public string GroupId { get; set; }
    public string ClientId { get; set; }
    public string InstanceId { get; set; }
    public string AccessKeyId { get; set; }
    public string AccessKeySecret { get; set; }
}

/// <summary>
/// 阿里云认证信息DTO
/// </summary>
public class AliyunAuthInfoDto
{
    public string ClientId { get; set; }
    public string Username { get; set; }
    public string Password { get; set; }
    public DateTime GenerateTime { get; set; }
    public long ExpiresIn { get; set; }
}


/// <summary>
/// 主题验证结果DTO
/// </summary>
public class TopicValidationResultDto
{
    public string Topic { get; set; }
    public bool IsValid { get; set; }
    public string ErrorMessage { get; set; }
    public DateTime ValidationTime { get; set; }
}

/// <summary>
/// 配置历史DTO
/// </summary>
public class ConfigHistoryDto
{
    public string Id { get; set; }
    public string ConfigType { get; set; }
    public string ChangeDescription { get; set; }
    public DateTime ChangeTime { get; set; }
    public string OperatorId { get; set; }
    public string OperatorName { get; set; }
    public string OldValue { get; set; }
    public string NewValue { get; set; }
}
