// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application.Entity;
using Admin.NET.Application.Service.Cache;
using Admin.NET.Plugin.GreenDisplay.Service;
using Admin.NET.Plugin.GreenDisplay.Service.Dto;
using Furion.DatabaseAccessor;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace Admin.NET.Application.Service;

/// <summary>
/// AP和设备状态同步后台服务
/// </summary>
public class APSyncBackgroundService : BackgroundService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ILogger<APSyncBackgroundService> _logger;
    private readonly APDeviceCacheService _cacheService;
    private Timer _syncTimer;
    private readonly TimeSpan _syncInterval = TimeSpan.FromSeconds(15); // 15秒同步一次，减少数据库压力
    
    // SignalR推送限流控制
    private readonly SemaphoreSlim _signalRSemaphore = new(1, 1);
    private readonly SemaphoreSlim _apPushSemaphore = new(1, 1);
    private readonly SemaphoreSlim _devicePushSemaphore = new(1, 1);
    
    // 数据库连接限流控制 - 使用更保守的并发数，避免MySQL连接重用问题
    private readonly SemaphoreSlim _dbConnectionSemaphore = new(1, 1); // 限制为单个并发数据库连接，避免连接冲突

    // 数据库操作重试配置
    private const int MaxRetries = 5; // 增加重试次数
    private const int BaseDelayMs = 800; // 增加基础延迟时间

    public APSyncBackgroundService(
        IServiceScopeFactory serviceScopeFactory,
        ILogger<APSyncBackgroundService> logger,
        APDeviceCacheService cacheService)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _logger = logger;
        _cacheService = cacheService;
    }

    /// <summary>
    /// 启动后台服务
    /// </summary>
    /// <param name="stoppingToken">停止令牌</param>
    /// <returns></returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("AP状态同步后台服务启动，同步间隔：{Interval}秒", _syncInterval.TotalSeconds);

        // 等待应用程序完全启动
        await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);

        // 检查数据库连接健康状态
        if (!await CheckDatabaseHealthAsync(stoppingToken))
        {
            _logger.LogError("数据库连接健康检查失败，后台服务将延迟启动");
            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
        }

        // 创建定时器
        _syncTimer = new Timer(
            callback: async _ => await SyncAllStatusAsync(),
            state: null,
            dueTime: TimeSpan.Zero, // 立即开始第一次同步
            period: _syncInterval);

        try
        {
            // 保持服务运行直到取消
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("AP状态同步后台服务正在停止...");
        }
        finally
        {
            _syncTimer?.Dispose();
        }
    }

    /// <summary>
    /// 同步所有状态的核心方法（AP和设备）
    /// </summary>
    /// <returns></returns>
    private async Task SyncAllStatusAsync()
    {
        try
        {
            // 并行执行提高效率，使用信号量控制并发
            var apTask = SyncAPStatusAsync();
            var deviceTask = SyncDeviceStatusAsync();
            
            // 等待两个任务完成
            await Task.WhenAll(apTask, deviceTask);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步所有状态时发生异常");
        }
    }

    /// <summary>
    /// 同步AP状态的核心方法
    /// </summary>
    /// <returns></returns>
    private async Task SyncAPStatusAsync()
    {
        _logger.LogDebug("开始同步AP状态");

        try
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(8)); // 设置超时时间略小于定时器间隔

            // 获取第三方平台的AP数据（优先从缓存获取）
            var thirdPartyAPs = _cacheService.GetThirdPartyAPs();
            if (thirdPartyAPs == null || thirdPartyAPs.Count == 0)
            {
                // 缓存中没有数据，从第三方平台获取
                using var scope = _serviceScopeFactory.CreateScope();
                var greenDisplayService = scope.ServiceProvider.GetRequiredService<GreenDisplayService>();
                thirdPartyAPs = await GetAllThirdPartyAPsAsync(greenDisplayService, cts.Token);

                if (thirdPartyAPs == null || thirdPartyAPs.Count == 0)
                {
                    _logger.LogDebug("第三方平台未返回AP数据");
                    return;
                }

                // 缓存第三方数据
                _cacheService.SetThirdPartyAPs(thirdPartyAPs);
            }
            else
            {
                _logger.LogDebug("从缓存获取到 {Count} 个第三方AP数据", thirdPartyAPs.Count);
            }

            // 获取本地AP数据 - 优先从缓存获取
            var localAPs = _cacheService.GetAllAccessPoints();
            if (localAPs == null || localAPs.Count == 0)
            {
                // 缓存中没有数据，从数据库获取
                localAPs = await ExecuteWithRetryAsync(async () =>
                {
                    using var scope = _serviceScopeFactory.CreateScope();
                    var accessPointsRep = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<AccessPoints>>();
                    return await accessPointsRep.AsQueryable().ToListAsync();
                }, "获取本地AP数据", cts.Token);

                if (localAPs == null)
                {
                    _logger.LogError("获取本地AP数据失败，已达到最大重试次数");
                    return;
                }

                // 缓存本地数据
                _cacheService.SetAllAccessPoints(localAPs);
            }
            else
            {
                _logger.LogDebug("从缓存获取到 {Count} 个本地AP数据", localAPs.Count);
            }
            
            var localAPDict = localAPs.ToDictionary(ap => ap.mac_address, ap => ap);

            var updatedAPs = new List<AccessPoints>();

            // 比较并更新状态
            foreach (var thirdPartyAP in thirdPartyAPs)
            {
                if (string.IsNullOrEmpty(thirdPartyAP.ApMac) || !localAPDict.TryGetValue(thirdPartyAP.ApMac, out var localAP))
                    continue;

                var newStatus = thirdPartyAP.Status;
                var hasStatusChange = localAP.ap_status != newStatus;
                var hasIpChange = !string.IsNullOrEmpty(thirdPartyAP.ApIp) && localAP.ip_address != thirdPartyAP.ApIp;
                var hasVersionChange = !string.IsNullOrEmpty(thirdPartyAP.Version) && localAP.firmware_version != thirdPartyAP.Version;
                //46:C1:01:01:22:87
                if (hasStatusChange || hasIpChange || hasVersionChange)
                {
                    if (hasStatusChange)
                    {
                        var oldStatus = localAP.ap_status;
                        localAP.ap_status = newStatus;
                        _logger.LogDebug("AP状态变更：MAC={Mac}, 旧状态={OldStatus}, 新状态={NewStatus}",
                            thirdPartyAP.ApMac, oldStatus?.ToString() ?? "null", newStatus.ToString());
                    }
                    if (hasIpChange)
                    {
                        localAP.ip_address = thirdPartyAP.ApIp;
                        _logger.LogDebug("AP IP变更：MAC={Mac}, 新IP={NewIp}", thirdPartyAP.ApMac, thirdPartyAP.ApIp);
                    }

                    if (hasVersionChange)
                    {
                        localAP.firmware_version = thirdPartyAP.Version;
                        _logger.LogDebug("AP版本变更：MAC={Mac}, 新版本={NewVersion}", thirdPartyAP.ApMac, thirdPartyAP.Version);
                    }

                    updatedAPs.Add(localAP);
                }
            }

            // 批量更新数据库 - 使用增强的重试机制
            if (updatedAPs.Count > 0)
            {
                var updateSuccess = await ExecuteWithRetryAsync(async () =>
                {
                    using var updateScope = _serviceScopeFactory.CreateScope();
                    var updateAccessPointsRep = updateScope.ServiceProvider.GetRequiredService<SqlSugarRepository<AccessPoints>>();
                    var hubContext = updateScope.ServiceProvider.GetRequiredService<IHubContext<APStatusHub>>();

                    await updateAccessPointsRep.UpdateRangeAsync(updatedAPs);
                    _logger.LogInformation("批量更新AP状态完成，更新数量：{Count}", updatedAPs.Count);

                    // 通过SignalR推送状态变更通知
                    await NotifyAPStatusChangesAsync(hubContext, updatedAPs);
                    return true;
                }, "批量更新AP状态", cts.Token);

                if (updateSuccess)
                {
                    // 更新缓存中的AP状态
                    _cacheService.UpdateAccessPointsStatus(updatedAPs);
                }
                else
                {
                    _logger.LogError("批量更新AP状态失败，已达到最大重试次数");
                }
            }
            else
            {
                _logger.LogDebug("无AP状态变更");
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("同步AP状态操作超时");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步AP状态时发生异常");
        }
    }

    /// <summary>
    /// 获取第三方平台所有AP数据
    /// </summary>
    /// <param name="greenDisplayService">绿显服务</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>AP数据列表</returns>
    private async Task<List<APOutput>> GetAllThirdPartyAPsAsync(GreenDisplayService greenDisplayService, CancellationToken cancellationToken)
    {
        var allAPs = new List<APOutput>();
        int pageNo = 1;
        const int pageSize = 50; // 每页获取50条数据

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var queryInput = new QueryAPInput
                {
                    PageNo = pageNo,
                    PageSize = pageSize
                };

                var response = await greenDisplayService.GetAPListAsync(queryInput);

                if (response?.list == null || response.list.Count == 0)
                {
                    _logger.LogDebug("第 {PageNo} 页无数据，结束获取", pageNo);
                    break;
                }

                allAPs.AddRange(response.list);

                _logger.LogDebug("获取第 {PageNo} 页数据成功，本页 {PageCount} 条，总计 {TotalCount} 条",
                    pageNo, response.list.Count, allAPs.Count);

                // 如果当前页数据少于页大小，说明已经是最后一页
                if (response.list.Count < pageSize)
                {
                    break;
                }

                pageNo++;

                // 添加延迟避免频繁请求
                await Task.Delay(50, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取第三方平台AP数据时发生异常，页码: {PageNo}", pageNo);
                throw;
            }
        }

        return allAPs;
    }



    /// <summary>
    /// 通过SignalR推送AP状态变更通知
    /// </summary>
    /// <param name="hubContext">Hub上下文</param>
    /// <param name="updatedAPs">更新的AP列表</param>
    /// <returns></returns>
    private async Task NotifyAPStatusChangesAsync(IHubContext<APStatusHub> hubContext, List<AccessPoints> updatedAPs)
    {
        // 使用信号量控制AP推送并发
        await _apPushSemaphore.WaitAsync();
        try
        {
            var statusUpdates = updatedAPs.Select(ap => new
            {
                MacAddress = ap.mac_address,
                ApName = ap.ap_name,
                Status = ap.ap_status,
                IpAddress = ap.ip_address,
                FirmwareVersion = ap.firmware_version,
                UpdateTime = DateTime.Now
            }).ToList();

            // 向所有连接的客户端推送状态更新
            await hubContext.Clients.All.SendAsync("APStatusUpdated", statusUpdates);

            _logger.LogDebug("已通过SignalR推送AP状态变更通知，数量：{Count}", updatedAPs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "推送AP状态变更通知时发生异常");
        }
        finally
        {
            _apPushSemaphore.Release();
        }
    }

    /// <summary>
    /// 同步设备状态的核心方法
    /// </summary>
    /// <returns></returns>
    private async Task SyncDeviceStatusAsync()
    {
        _logger.LogDebug("开始同步设备状态");

        try
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(8)); // 设置超时时间略小于定时器间隔

            // 获取第三方平台的设备数据（优先从缓存获取）
            var thirdPartyDevices = await GetThirdPartyDevicesWithCacheAsync(cts.Token);
            if (thirdPartyDevices == null || thirdPartyDevices.Count == 0)
            {
                _logger.LogDebug("未获取到第三方设备数据");
                return;
            }

            // 获取本地设备数据 - 优先从缓存获取
            var localDevices = await GetLocalDevicesWithCacheAsync(cts.Token);
            if (localDevices == null || localDevices.Count == 0)
            {
                _logger.LogError("未获取到本地设备数据");
                return;
            }

            // 比较设备状态并准备更新数据
            var updatedDevices = CompareAndPrepareDeviceUpdates(localDevices, thirdPartyDevices);

            // 批量更新数据库和推送通知
            if (updatedDevices.Count > 0)
            {
                await UpdateDevicesAndNotifyAsync(updatedDevices, cts.Token);
            }
            else
            {
                _logger.LogDebug("无设备状态变更");
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("同步设备状态操作超时");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "同步设备状态时发生异常");
        }
    }

    /// <summary>
    /// 获取第三方平台所有设备数据
    /// </summary>
    /// <param name="greenDisplayService">绿显服务</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备数据列表</returns>
    private async Task<List<DeviceInfo>> GetAllThirdPartyDevicesAsync(GreenDisplayService greenDisplayService, CancellationToken cancellationToken)
    {
        var allDevices = new List<DeviceInfo>();
        int pageNo = 1;
        const int pageSize = 50; // 每页获取50条数据

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var queryInput = new QueryDevicesInput
                {
                    PageNo = pageNo,
                    PageSize = pageSize
                };

                var response = await greenDisplayService.QueryDevicesAsync(queryInput);

                if (response?.list == null || response.list.Count == 0)
                {
                    _logger.LogDebug("第 {PageNo} 页无设备数据，结束获取", pageNo);
                    break;
                }

                allDevices.AddRange(response.list);

                _logger.LogDebug("获取第 {PageNo} 页设备数据成功，本页 {PageCount} 条，总计 {TotalCount} 条",
                    pageNo, response.list.Count, allDevices.Count);

                // 如果当前页数据少于页大小，说明已经是最后一页
                if (response.list.Count < pageSize)
                {
                    break;
                }

                pageNo++;

                // 添加延迟避免频繁请求
                await Task.Delay(50, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取第三方平台设备数据时发生异常，页码: {PageNo}", pageNo);
                throw;
            }
        }

        return allDevices;
    }

    /// <summary>
    /// 获取第三方设备数据（优先从缓存获取）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>第三方设备数据列表</returns>
    private async Task<List<DeviceInfo>> GetThirdPartyDevicesWithCacheAsync(CancellationToken cancellationToken)
    {
        var thirdPartyDevices = _cacheService.GetThirdPartyDevices();
        if (thirdPartyDevices != null && thirdPartyDevices.Count > 0)
        {
            _logger.LogDebug("从缓存获取到 {Count} 个第三方设备数据", thirdPartyDevices.Count);
            return thirdPartyDevices;
        }

        // 缓存中没有数据，从第三方平台获取
        using var scope = _serviceScopeFactory.CreateScope();
        var greenDisplayService = scope.ServiceProvider.GetRequiredService<GreenDisplayService>();

        thirdPartyDevices = await GetAllThirdPartyDevicesAsync(greenDisplayService, cancellationToken);
        if (thirdPartyDevices == null || thirdPartyDevices.Count == 0)
        {
            _logger.LogDebug("第三方平台未返回设备数据");
            return new List<DeviceInfo>();
        }

        // 缓存第三方数据
        _cacheService.SetThirdPartyDevices(thirdPartyDevices);
        _logger.LogDebug("从第三方平台获取到 {Count} 个设备数据并已缓存", thirdPartyDevices.Count);

        return thirdPartyDevices;
    }

    /// <summary>
    /// 获取本地设备数据（优先从缓存获取）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>本地设备数据列表</returns>
    private async Task<List<Devices>> GetLocalDevicesWithCacheAsync(CancellationToken cancellationToken)
    {
        var localDevices = _cacheService.GetAllDevices();
        if (localDevices != null && localDevices.Count > 0)
        {
            _logger.LogDebug("从缓存获取到 {Count} 个本地设备数据", localDevices.Count);
            return localDevices;
        }

        // 缓存中没有数据，从数据库获取
        localDevices = await ExecuteWithRetryAsync(async () =>
        {
            using var deviceScope = _serviceScopeFactory.CreateScope();
            var scopedDevicesRep = deviceScope.ServiceProvider.GetRequiredService<SqlSugarRepository<Devices>>();
            return await scopedDevicesRep.AsQueryable().Where(d => !d.IsDelete).ToListAsync();
        }, "获取本地设备数据", cancellationToken);

        if (localDevices == null || localDevices.Count == 0)
        {
            _logger.LogError("获取本地设备数据失败");
            return new List<Devices>();
        }

        // 缓存本地数据
        _cacheService.SetAllDevices(localDevices);
        _logger.LogDebug("从数据库获取到 {Count} 个本地设备数据并已缓存", localDevices.Count);

        return localDevices;
    }

    /// <summary>
    /// 将设备状态映射为整数
    /// </summary>
    /// <param name="result">设备状态结果</param>
    /// <returns>状态整数值</returns>
    private int? MapDeviceStatusToInt(int result)
    {
        return result switch
        {
            0 => 0,  // 正常/在线
            1 => 1,  // 离线
            _ => null
        };
    }

    /// <summary>
    /// 比较设备状态并准备更新数据
    /// </summary>
    /// <param name="localDevices">本地设备列表</param>
    /// <param name="thirdPartyDevices">第三方设备列表</param>
    /// <returns>需要更新的设备列表</returns>
    private List<Devices> CompareAndPrepareDeviceUpdates(List<Devices> localDevices, List<DeviceInfo> thirdPartyDevices)
    {
        var localDeviceDict = localDevices.ToDictionary(device => device.mac_address, device => device);
        var updatedDevices = new List<Devices>();

        foreach (var thirdPartyDevice in thirdPartyDevices)
        {
            if (string.IsNullOrEmpty(thirdPartyDevice.labelMac) ||
                !localDeviceDict.TryGetValue(thirdPartyDevice.labelMac, out var localDevice))
                continue;

            var newStatus = MapDeviceStatusToInt(thirdPartyDevice.result);
            var newBatteryLevel = thirdPartyDevice.battery > 0 ? thirdPartyDevice.battery : (int?)null;
            var newSignalStrength = !string.IsNullOrEmpty(thirdPartyDevice.signals) ?
                (int.TryParse(thirdPartyDevice.signals, out int signal) ? signal : (int?)null) : null;

            var hasStatusChange = localDevice.status != newStatus;
            var hasBatteryChange = localDevice.battery_level != newBatteryLevel;
            var hasSignalChange = localDevice.signal_strength != newSignalStrength;

            if (hasStatusChange || hasBatteryChange || hasSignalChange)
            {
                if (hasStatusChange)
                {
                    var oldStatus = localDevice.status;
                    localDevice.status = newStatus;
                    _logger.LogDebug("设备状态变更：MAC={Mac}, 旧状态={OldStatus}, 新状态={NewStatus}",
                        thirdPartyDevice.labelMac, oldStatus?.ToString() ?? "null", newStatus?.ToString() ?? "null");
                }
                if (hasBatteryChange)
                {
                    localDevice.battery_level = newBatteryLevel;
                    _logger.LogDebug("设备电量变更：MAC={Mac}, 新电量={NewBattery}", thirdPartyDevice.labelMac, newBatteryLevel);
                }
                if (hasSignalChange)
                {
                    localDevice.signal_strength = newSignalStrength;
                    _logger.LogDebug("设备信号强度变更：MAC={Mac}, 新信号强度={NewSignal}", thirdPartyDevice.labelMac, newSignalStrength);
                }

                updatedDevices.Add(localDevice);
            }
        }

        return updatedDevices;
    }

    /// <summary>
    /// 更新设备数据并发送通知
    /// </summary>
    /// <param name="updatedDevices">需要更新的设备列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    private async Task UpdateDevicesAndNotifyAsync(List<Devices> updatedDevices, CancellationToken cancellationToken)
    {
        var updateSuccess = await ExecuteWithRetryAsync(async () =>
        {
            using var updateScope = _serviceScopeFactory.CreateScope();
            var scopedUpdateDevicesRep = updateScope.ServiceProvider.GetRequiredService<SqlSugarRepository<Devices>>();
            var deviceHubContext = updateScope.ServiceProvider.GetRequiredService<IHubContext<APStatusHub>>();

            await scopedUpdateDevicesRep.UpdateRangeAsync(updatedDevices);
            _logger.LogInformation("批量更新设备状态完成，更新数量：{Count}", updatedDevices.Count);

            // 通过SignalR推送状态变更通知
            await NotifyDeviceStatusChangesAsync(deviceHubContext, updatedDevices);
            return true;
        }, "批量更新设备状态", cancellationToken);

        if (updateSuccess)
        {
            // 更新缓存中的设备状态
            _cacheService.UpdateDevicesStatus(updatedDevices);
            _logger.LogDebug("设备状态缓存更新完成");
        }
        else
        {
            _logger.LogError("批量更新设备状态失败，已达到最大重试次数");
        }
    }

    /// <summary>
    /// 通过SignalR推送设备状态变更通知
    /// </summary>
    /// <param name="hubContext">Hub上下文</param>
    /// <param name="updatedDevices">更新的设备列表</param>
    /// <returns></returns>
    private async Task NotifyDeviceStatusChangesAsync(IHubContext<APStatusHub> hubContext, List<Devices> updatedDevices)
    {
        // 使用信号量控制设备推送并发
        await _devicePushSemaphore.WaitAsync();
        try
        {
            var statusUpdates = updatedDevices.Select(device => new
            {
                MacAddress = device.mac_address,
                DeviceName = device.device_name,
                Status = device.status,
                BatteryLevel = device.battery_level,
                SignalStrength = device.signal_strength,
                UpdateTime = DateTime.Now
            }).ToList();

            // 向所有连接的客户端推送状态更新
            await hubContext.Clients.All.SendAsync("DeviceStatusUpdated", statusUpdates);

            _logger.LogDebug("已通过SignalR推送设备状态变更通知，数量：{Count}", updatedDevices.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "推送设备状态变更通知时发生异常");
        }
        finally
        {
            _devicePushSemaphore.Release();
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns></returns>
    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("AP状态同步后台服务正在停止...");
        
        _syncTimer?.Dispose();
        
        await base.StopAsync(cancellationToken);
        
        _logger.LogInformation("AP状态同步后台服务已停止");
    }

    /// <summary>
    /// 检查数据库连接健康状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康状态</returns>
    private async Task<bool> CheckDatabaseHealthAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var accessPointsRep = scope.ServiceProvider.GetRequiredService<SqlSugarRepository<AccessPoints>>();

            // 简单的数据库连接测试
            await accessPointsRep.AsQueryable().Take(1).ToListAsync();
            _logger.LogInformation("数据库连接健康检查通过");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库连接健康检查失败");
            return false;
        }
    }

    /// <summary>
    /// 带重试机制的数据库操作执行方法
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="operation">要执行的操作</param>
    /// <param name="operationName">操作名称（用于日志）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>操作结果，失败时返回默认值</returns>
    private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName, CancellationToken cancellationToken = default)
    {
        int retryCount = 0;

        while (retryCount < MaxRetries)
        {
            await _dbConnectionSemaphore.WaitAsync(cancellationToken);
            try
            {
                return await operation();
            }
            catch (SqlSugar.SqlSugarException ex) when (ex.Message.Contains("Cannot Open when State is Connecting") && retryCount < MaxRetries - 1)
            {
                retryCount++;
                _logger.LogWarning($"{operationName} - 数据库连接冲突，第{retryCount}次重试...");
                await Task.Delay(BaseDelayMs * retryCount, cancellationToken);
            }
            catch (System.InvalidOperationException ex) when ((ex.Message.Contains("Can't replace active reader") || ex.Message.Contains("This MySqlConnection is already in use")) && retryCount < MaxRetries - 1)
            {
                retryCount++;
                _logger.LogWarning($"{operationName} - 数据库连接/读取器冲突，第{retryCount}次重试...");
                await Task.Delay(BaseDelayMs * retryCount + 200, cancellationToken); // 增加延迟时间
            }
            catch (MySqlConnector.MySqlException ex) when ((ex.Message.Contains("Failed to read the result set") || ex.Message.Contains("Connection must be valid and open")) && retryCount < MaxRetries - 1)
            {
                retryCount++;
                _logger.LogWarning($"{operationName} - MySQL连接异常，第{retryCount}次重试...");
                await Task.Delay(BaseDelayMs * retryCount + 300, cancellationToken); // 增加延迟时间
            }
            catch (Exception ex) when (ex.Message.Contains("This MySqlConnection is already in use") && retryCount < MaxRetries - 1)
            {
                retryCount++;
                _logger.LogWarning($"{operationName} - MySQL连接重用冲突，第{retryCount}次重试...");
                await Task.Delay(BaseDelayMs * retryCount + 400, cancellationToken); // 更长的延迟时间
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{operationName} - 执行时发生异常");
                return default(T);
            }
            finally
            {
                _dbConnectionSemaphore.Release();
            }
        }

        _logger.LogError($"{operationName} - 已达到最大重试次数 {MaxRetries}");
        return default(T);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        _syncTimer?.Dispose();
        _signalRSemaphore?.Dispose();
        _apPushSemaphore?.Dispose();
        _devicePushSemaphore?.Dispose();
        _dbConnectionSemaphore?.Dispose();
        base.Dispose();
        GC.SuppressFinalize(this);
    }
}