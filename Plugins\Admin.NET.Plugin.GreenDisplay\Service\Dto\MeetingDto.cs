// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.GreenDisplay.Service;

/// <summary>
/// 会议室创建输入
/// </summary>
public class CreateMeetingRoomInput
{
    /// <summary>
    /// 会议室名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 模板ID
    /// </summary>
    public int? TemplateId { get; set; }

    /// <summary>
    /// 桌牌MAC地址
    /// </summary>
    public string LabelMac { get; set; }
}

/// <summary>
/// 会议室更新输入
/// </summary>
public class UpdateMeetingRoomInput
{
    /// <summary>
    /// 会议室ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 会议室名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }
}

/// <summary>
/// 会议室查询输入
/// </summary>
public class QueryMeetingRoomsInput
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageNo { get; set; } = 1;

    /// <summary>
    /// 每页记录数
    /// </summary>
    public int PageSize { get; set; } = 100;
}

/// <summary>
/// 会议人员创建输入
/// </summary>
public class CreateMeetingStaffInput
{
    /// <summary>
    /// 人员编号
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string Mobile { get; set; }

    /// <summary>
    /// 公司
    /// </summary>
    public string Company { get; set; }

    /// <summary>
    /// 职位
    /// </summary>
    public string Position { get; set; }

    /// <summary>
    /// 部门
    /// </summary>
    public string dept { get; set; }

    /// <summary>
    /// 自定义字段1
    /// </summary>
    public string Field1 { get; set; }

    /// <summary>
    /// 自定义字段2
    /// </summary>
    public string Field2 { get; set; }

    /// <summary>
    /// 自定义字段3
    /// </summary>
    public string Field3 { get; set; }

    /// <summary>
    /// 自定义字段4
    /// </summary>
    public string Field4 { get; set; }

    /// <summary>
    /// 自定义字段5
    /// </summary>
    public string Field5 { get; set; }

    /// <summary>
    /// 自定义字段6
    /// </summary>
    public string Field6 { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }
}

/// <summary>
/// 会议人员更新输入
/// </summary>
public class UpdateMeetingStaffInput : CreateMeetingStaffInput
{
    /// <summary>
    /// 人员ID
    /// </summary>
    public int Id { get; set; }
}

/// <summary>
/// 会议人员查询输入
/// </summary>
public class QueryMeetingStaffInput
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageNo { get; set; } = 1;

    /// <summary>
    /// 每页记录数
    /// </summary>
    public int PageSize { get; set; } = 100;

    /// <summary>
    /// 工号
    /// </summary>
    public string code { get; set; } = string.Empty;
    /// 姓名
    /// </summary>
    public string name { get; set; } = string.Empty;
    /// <summary>
    /// 手机号
    /// </summary>
    public string mobile { get; set; } = string.Empty;
    /// <summary>
    /// 公司
    /// </summary>
    public string company { get; set; } = string.Empty;
    /// <summary>
    /// 职位
    /// </summary>
    public string position { get; set; } = string.Empty;
}