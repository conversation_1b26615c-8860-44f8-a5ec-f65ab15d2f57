// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using MQTTnet.Protocol;
using System.Collections.Concurrent;
using System.Text.Json;

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// EMQX服务
/// </summary>
[ApiDescriptionSettings("EMQX", Name = "MQTT服务", Order = 100)]
public class EMQXService : IDynamicApiController, ITransient
{
    private readonly EMQXOptions _options;
    private readonly ConcurrentDictionary<string, IMqttClient> _clients;
    private readonly ConcurrentDictionary<string, MqttConnectionDto> _connections;
    private readonly ConcurrentDictionary<string, List<MqttSubscriptionDto>> _subscriptions;
    private readonly ConcurrentDictionary<string, MqttStatisticsDto> _statistics;
    private readonly object _lockObject = new();

    public EMQXService(IOptions<EMQXOptions> options)
    {
        _options = options.Value;
        _clients = new ConcurrentDictionary<string, IMqttClient>();
        _connections = new ConcurrentDictionary<string, MqttConnectionDto>();
        _subscriptions = new ConcurrentDictionary<string, List<MqttSubscriptionDto>>();
        _statistics = new ConcurrentDictionary<string, MqttStatisticsDto>();
    }

    /// <summary>
    /// 创建MQTT连接
    /// </summary>
    /// <param name="input">连接参数</param>
    /// <returns>连接结果</returns>
    [HttpPost("/api/emqx/connect")]
    public async Task<bool> ConnectAsync(MqttConnectionInput input)
    {
        var clientId = input.UseAliyunAuth 
            ? AliyunMqttSignatureUtil.GenerateClientId(input.GroupId, input.ClientId)
            : $"{_options.ClientIdPrefix}_{input.GroupId}_{input.ClientId}";

        // 如果客户端已存在，先断开连接
        if (_clients.TryGetValue(clientId, out var existingClient))
        {
            await DisconnectAsync(clientId);
        }

        var factory = new MqttFactory();
        var client = factory.CreateMqttClient();

        // 配置连接选项
        var optionsBuilder = new MqttClientOptionsBuilder()
            .WithTcpServer(_options.Server, _options.Port)
            .WithClientId(clientId)
            .WithCleanSession(input.CleanSession)
            .WithKeepAlivePeriod(TimeSpan.FromSeconds(input.KeepAlivePeriod));

        // 设置认证信息
        if (input.UseAliyunAuth)
        {
            var username = AliyunMqttSignatureUtil.GenerateUsername(input.AccessKeyId, input.InstanceId);
            var password = AliyunMqttSignatureUtil.GeneratePassword(_options.AliyunMqtt.AccessKeySecret, clientId);
            optionsBuilder.WithCredentials(username, password);
        }
        else if (!string.IsNullOrEmpty(input.Username))
        {
            optionsBuilder.WithCredentials(input.Username, input.Password);
        }
        else if (!string.IsNullOrEmpty(_options.Username))
        {
            optionsBuilder.WithCredentials(_options.Username, _options.Password);
        }

        // 配置TLS
        if (_options.UseTLS)
        {
            optionsBuilder.WithTls();
        }

        var options = optionsBuilder.Build();

        // 设置事件处理
        client.ConnectedAsync += async e =>
        {
            var connection = new MqttConnectionDto
            {
                ClientId = clientId,
                GroupId = input.GroupId,
                Status = MqttConnectionStatusEnum.Connected,
                ConnectedTime = DateTime.Now,
                LastActiveTime = DateTime.Now,
                OnlineDuration = 0,
                IpAddress = "Unknown",
                UserAgent = "Admin.NET.Plugin.EMQX",
                SubscriptionCount = 0,
                SentMessageCount = 0,
                ReceivedMessageCount = 0
            };
            _connections.AddOrUpdate(clientId, connection, (key, old) => connection);
            await Task.CompletedTask;
        };

        client.DisconnectedAsync += async e =>
        {
            if (_connections.TryGetValue(clientId, out var connection))
            {
                connection.Status = MqttConnectionStatusEnum.Disconnected;
                if (connection.ConnectedTime.HasValue)
                {
                    connection.OnlineDuration = (long)(DateTime.Now - connection.ConnectedTime.Value).TotalSeconds;
                }
            }
            await Task.CompletedTask;
        };

        client.ApplicationMessageReceivedAsync += async e =>
        {
            if (_connections.TryGetValue(clientId, out var connection))
            {
                connection.ReceivedMessageCount++;
                connection.LastActiveTime = DateTime.Now;
            }
            await Task.CompletedTask;
        };

        try
        {
            // 连接到MQTT服务器
            var result = await client.ConnectAsync(options);
            if (result.ResultCode == MqttClientConnectResultCode.Success)
            {
                _clients.AddOrUpdate(clientId, client, (key, old) => client);
                return true;
            }
            else
            {
                throw Oops.Oh($"MQTT连接失败: {result.ResultCode} - {result.ReasonString}");
            }
        }
        catch (Exception ex)
        {
            if (_connections.TryGetValue(clientId, out var connection))
            {
                connection.Status = MqttConnectionStatusEnum.Failed;
            }
            throw Oops.Oh($"MQTT连接异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 断开MQTT连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>断开结果</returns>
    [HttpPost("/api/emqx/disconnect")]
    public async Task<bool> DisconnectAsync(string clientId)
    {
        if (_clients.TryRemove(clientId, out var client))
        {
            try
            {
                await client.DisconnectAsync();
                client.Dispose();
                
                if (_connections.TryGetValue(clientId, out var connection))
                {
                    connection.Status = MqttConnectionStatusEnum.Disconnected;
                    if (connection.ConnectedTime.HasValue)
                    {
                        connection.OnlineDuration = (long)(DateTime.Now - connection.ConnectedTime.Value).TotalSeconds;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                throw Oops.Oh($"断开MQTT连接异常: {ex.Message}");
            }
        }
        return false;
    }

    /// <summary>
    /// 发布消息
    /// </summary>
    /// <param name="input">发布参数</param>
    /// <returns>发布结果</returns>
    [HttpPost("/api/emqx/publish")]
    public async Task<bool> PublishAsync(MqttPublishInput input)
    {
        var clientId = string.IsNullOrEmpty(input.SenderClientId) 
            ? _clients.Keys.FirstOrDefault() 
            : input.SenderClientId;

        if (string.IsNullOrEmpty(clientId) || !_clients.TryGetValue(clientId, out var client))
        {
            throw Oops.Oh("未找到可用的MQTT客户端连接");
        }

        var message = new MqttApplicationMessageBuilder()
            .WithTopic(input.Topic)
            .WithPayload(input.Payload)
            .WithQualityOfServiceLevel((MqttQualityOfServiceLevel)(int)input.QosLevel)
            .WithRetainFlag(input.Retain)
            .Build();

        try
        {
            var result = await client.PublishAsync(message);
            if (result.IsSuccess)
            {
                // 更新统计信息
                if (_connections.TryGetValue(clientId, out var connection))
                {
                    connection.SentMessageCount++;
                    connection.LastActiveTime = DateTime.Now;
                }
                return true;
            }
            else
            {
                throw Oops.Oh($"消息发布失败: {result.ReasonCode} - {result.ReasonString}");
            }
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"消息发布异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 订阅主题
    /// </summary>
    /// <param name="input">订阅参数</param>
    /// <returns>订阅结果</returns>
    [HttpPost("/api/emqx/subscribe")]
    public async Task<bool> SubscribeAsync(MqttSubscribeInput input)
    {
        var clientId = string.IsNullOrEmpty(input.ClientId) 
            ? _clients.Keys.FirstOrDefault() 
            : input.ClientId;

        if (string.IsNullOrEmpty(clientId) || !_clients.TryGetValue(clientId, out var client))
        {
            throw Oops.Oh("未找到可用的MQTT客户端连接");
        }

        var subscribeOptions = new MqttClientSubscribeOptionsBuilder()
            .WithTopicFilter(f => f
                .WithTopic(input.Topic)
                .WithQualityOfServiceLevel((MqttQualityOfServiceLevel)(int)input.QosLevel))
            .Build();

        try
        {
            var result = await client.SubscribeAsync(subscribeOptions);
            if (result.Items.Any(item => item.ResultCode == MqttClientSubscribeResultCode.GrantedQoS0 ||
                                        item.ResultCode == MqttClientSubscribeResultCode.GrantedQoS1 ||
                                        item.ResultCode == MqttClientSubscribeResultCode.GrantedQoS2))
            {
                // 添加订阅记录
                var subscription = new MqttSubscriptionDto
                {
                    Topic = input.Topic,
                    QosLevel = input.QosLevel,
                    ClientId = clientId,
                    SubscribeTime = DateTime.Now,
                    ReceivedMessageCount = 0
                };

                _subscriptions.AddOrUpdate(clientId, 
                    new List<MqttSubscriptionDto> { subscription },
                    (key, existing) => 
                    {
                        existing.RemoveAll(s => s.Topic == input.Topic);
                        existing.Add(subscription);
                        return existing;
                    });

                // 更新连接统计
                if (_connections.TryGetValue(clientId, out var connection))
                {
                    connection.SubscriptionCount = _subscriptions[clientId].Count;
                }

                return true;
            }
            else
            {
                var failedItem = result.Items.First();
                throw Oops.Oh($"主题订阅失败: {failedItem.ResultCode}");
            }
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"主题订阅异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 取消订阅主题
    /// </summary>
    /// <param name="input">取消订阅参数</param>
    /// <returns>取消订阅结果</returns>
    [HttpPost("/api/emqx/unsubscribe")]
    public async Task<bool> UnsubscribeAsync(MqttUnsubscribeInput input)
    {
        var clientId = string.IsNullOrEmpty(input.ClientId) 
            ? _clients.Keys.FirstOrDefault() 
            : input.ClientId;

        if (string.IsNullOrEmpty(clientId) || !_clients.TryGetValue(clientId, out var client))
        {
            throw Oops.Oh("未找到可用的MQTT客户端连接");
        }

        var unsubscribeOptions = new MqttClientUnsubscribeOptionsBuilder()
            .WithTopicFilter(input.Topic)
            .Build();

        try
        {
            var result = await client.UnsubscribeAsync(unsubscribeOptions);
            if (result.Items.Any(item => item.ResultCode == MqttClientUnsubscribeResultCode.Success))
            {
                // 移除订阅记录
                if (_subscriptions.TryGetValue(clientId, out var subscriptions))
                {
                    subscriptions.RemoveAll(s => s.Topic == input.Topic);
                    
                    // 更新连接统计
                    if (_connections.TryGetValue(clientId, out var connection))
                    {
                        connection.SubscriptionCount = subscriptions.Count;
                    }
                }

                return true;
            }
            else
            {
                var failedItem = result.Items.First();
                throw Oops.Oh($"取消订阅失败: {failedItem.ResultCode}");
            }
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"取消订阅异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取连接列表
    /// </summary>
    /// <returns>连接列表</returns>
    [HttpGet("/api/emqx/connections")]
    public Task<List<MqttConnectionDto>> GetConnectionsAsync()
    {
        var connections = _connections.Values.ToList();
        
        // 更新在线时长
        foreach (var connection in connections.Where(c => c.Status == MqttConnectionStatusEnum.Connected && c.ConnectedTime.HasValue))
        {
            connection.OnlineDuration = (long)(DateTime.Now - connection.ConnectedTime.Value).TotalSeconds;
        }
        
        return Task.FromResult(connections);
    }

    /// <summary>
    /// 获取连接状态
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>连接状态</returns>
    [HttpGet("/api/emqx/connection/status")]
    public Task<MqttConnectionDto> GetConnectionStatusAsync(string clientId)
    {
        if (_connections.TryGetValue(clientId, out var connection))
        {
            // 更新在线时长
            if (connection.Status == MqttConnectionStatusEnum.Connected && connection.ConnectedTime.HasValue)
            {
                connection.OnlineDuration = (long)(DateTime.Now - connection.ConnectedTime.Value).TotalSeconds;
            }
            return Task.FromResult(connection);
        }
        
        // 如果没有找到连接记录，返回默认状态
        var defaultConnection = new MqttConnectionDto
        {
            ClientId = clientId,
            Status = MqttConnectionStatusEnum.Disconnected,
            ConnectedTime = null,
            LastActiveTime = null,
            OnlineDuration = 0,
            IpAddress = "Unknown",
            UserAgent = "Unknown",
            SubscriptionCount = 0,
            SentMessageCount = 0,
            ReceivedMessageCount = 0
        };
        
        return Task.FromResult(defaultConnection);
    }

    /// <summary>
    /// 获取订阅列表
    /// </summary>
    /// <param name="clientId">客户端ID(可选)</param>
    /// <returns>订阅列表</returns>
    [HttpGet("/api/emqx/subscriptions")]
    public Task<List<MqttSubscriptionDto>> GetSubscriptionsAsync(string clientId = null)
    {
        var subscriptions = new List<MqttSubscriptionDto>();
        
        if (string.IsNullOrEmpty(clientId))
        {
            // 返回所有订阅
            foreach (var kvp in _subscriptions)
            {
                subscriptions.AddRange(kvp.Value);
            }
        }
        else if (_subscriptions.TryGetValue(clientId, out var clientSubscriptions))
        {
            subscriptions.AddRange(clientSubscriptions);
        }
        
        return Task.FromResult(subscriptions);
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    [HttpGet("/api/emqx/statistics")]
    public Task<MqttStatisticsDto> GetStatisticsAsync()
    {
        var connections = _connections.Values.ToList();
        var onlineConnections = connections.Count(c => c.Status == MqttConnectionStatusEnum.Connected);
        var totalConnections = connections.Count;
        var offlineConnections = totalConnections - onlineConnections;
        
        var statistics = new MqttStatisticsDto
        {
            TotalConnections = totalConnections,
            OnlineConnections = onlineConnections,
            OfflineConnections = offlineConnections,
            ConnectionSuccessRate = totalConnections > 0 ? (decimal)onlineConnections / totalConnections * 100 : 0,
            TotalSubscriptions = _subscriptions.Values.Sum(s => s.Count),
            ActiveSubscriptions = _subscriptions.Values.Sum(s => s.Count),
            TodaySentMessages = connections.Sum(c => c.SentMessageCount),
            TodayReceivedMessages = connections.Sum(c => c.ReceivedMessageCount),
            TotalSentMessages = connections.Sum(c => c.SentMessageCount),
            TotalReceivedMessages = connections.Sum(c => c.ReceivedMessageCount),
            AverageMessageSize = 0, // 需要实际统计
            ServerStatus = "Running",
            ServerStartTime = DateTime.Now.AddHours(-1), // 示例数据
            ServerUptime = 3600, // 示例数据
            MemoryUsage = 128, // 示例数据
            CpuUsage = 15.5m // 示例数据
        };
        
        return Task.FromResult(statistics);
    }

    /// <summary>
    /// 测试连接
    /// </summary>
    /// <returns>测试结果</returns>
    [HttpPost("/api/emqx/test-connection")]
    public async Task<bool> TestConnectionAsync()
    {
        var factory = new MqttFactory();
        var client = factory.CreateMqttClient();
        
        var options = new MqttClientOptionsBuilder()
            .WithTcpServer(_options.Server, _options.Port)
            .WithClientId($"test_{Guid.NewGuid():N}")
            .WithCleanSession(true)
            .WithKeepAlivePeriod(TimeSpan.FromSeconds(30));
            
        if (!string.IsNullOrEmpty(_options.Username))
        {
            options.WithCredentials(_options.Username, _options.Password);
        }
        
        if (_options.UseTLS)
        {
            options.WithTls();
        }
        
        try
        {
            var result = await client.ConnectAsync(options.Build());
            if (result.ResultCode == MqttClientConnectResultCode.Success)
            {
                await client.DisconnectAsync();
                return true;
            }
            return false;
        }
        catch
        {
            return false;
        }
        finally
        {
            client?.Dispose();
        }
    }
}