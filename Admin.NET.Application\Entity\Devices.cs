﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
namespace Admin.NET.Application.Entity;

/// <summary>
/// 蓝牙桌牌设备
/// </summary>
[Tenant("1300000000001")]
[SugarTable("devices", "蓝牙桌牌设备")]
public partial class Devices : EntityBaseDel
{
    /// <summary>
    /// 网关ID
    /// </summary>
    [SugarColumn(ColumnName = "ap_id", ColumnDescription = "网关ID")]
    public virtual long? ap_id { get; set; }
    
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "device_id", ColumnDescription = "设备ID", Length = 100)]
    public virtual string device_id { get; set; }
    
    /// <summary>
    /// 设备名称
    /// </summary>
    [SugarColumn(ColumnName = "device_name", ColumnDescription = "设备名称", Length = 200)]
    public virtual string? device_name { get; set; }
    
    /// <summary>
    /// MAC地址
    /// </summary>
    [SugarColumn(ColumnName = "mac_address", ColumnDescription = "MAC地址", Length = 17)]
    public virtual string? mac_address { get; set; }
    
    /// <summary>
    /// 1：姓名桌牌；2：价格标签
    /// </summary>
    [SugarColumn(ColumnName = "device_type", ColumnDescription = "1：姓名桌牌；2：价格标签")]
    public virtual int? device_type { get; set; }
    
    /// <summary>
    /// 0：在线；1离线
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "0：在线；1离线；")]
    public virtual int? status { get; set; }
    
    /// <summary>
    /// 电量
    /// </summary>
    [SugarColumn(ColumnName = "battery_level", ColumnDescription = "电量", DefaultValue = "100")]
    public virtual int? battery_level { get; set; }
    
    /// <summary>
    /// 信号强度
    /// </summary>
    [SugarColumn(ColumnName = "signal_strength", ColumnDescription = "信号强度", DefaultValue = "100")]
    public virtual int? signal_strength { get; set; }

    /// <summary>
    /// 固件版本
    /// </summary>
    [SugarColumn(ColumnName = "firmware_version", ColumnDescription = "固件版本", Length = 50)]
    public virtual string? firmware_version { get; set; }
    
    /// <summary>
    /// 绑定租户Id
    /// </summary>
    [SugarColumn(ColumnName = "BindTenantId", ColumnDescription = "绑定租户Id")]
    public virtual long? BindTenantId { get; set; }
    
    /// <summary>
    /// 绑定用户Id
    /// </summary>
    [SugarColumn(ColumnName = "BindUserId", ColumnDescription = "绑定用户Id")]
    public virtual long? BindUserId { get; set; }
    
}
