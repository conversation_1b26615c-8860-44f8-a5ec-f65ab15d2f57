fail: 2025-07-22 10:05:53.5587924 +08:00 Tuesday L System.Logging.StringLogging[0] #38 '00-57328c539064e510b2f27dbd752ad967-e428b19c8b8f861e-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:05:53——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:05:53.068',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700353539862597'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:05:53.071',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700353539862598') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:14:37.1066172 +08:00 Tuesday L System.Logging.StringLogging[0] #11 '00-7976d3bfb69e357a6f654a79b13e803e-2587b8062eaff285-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:14:37——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE ((`staff_code` IN ('20250205','20250206')) AND( `IsDelete` = @IsDelete1 ))  AND ( `IsDelete` = @IsDelete2 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      [Name]:@IsDelete2 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:47.8541054 +08:00 Tuesday L System.Logging.StringLogging[0] #11 '00-ce2f263d60ba2fff5cf28e17a350ad0b-fddfa0d698c40717-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:15:47——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE ((`staff_code` IN ('20250205','20250206')) AND( `IsDelete` = @IsDelete1 ))  AND ( `IsDelete` = @IsDelete2 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      [Name]:@IsDelete2 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:47.8934216 +08:00 Tuesday L System.Logging.UnitOfWork[0] #11 '00-ce2f263d60ba2fff5cf28e17a350ad0b-fddfa0d698c40717-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:47.9208442 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #11 '00-ce2f263d60ba2fff5cf28e17a350ad0b-fddfa0d698c40717-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 127.0.0.1
      ┣ 客户端源端口：                   57245
      ┣ 服务端 IP 地址：                 127.0.0.1
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-ce2f263d60ba2fff5cf28e17a350ad0b-fddfa0d698c40717-00
      ┣ 服务线程 ID：                    #11
      ┣ 执行耗时：                       213ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.rAWk09Tsxa9-iWLCrIuA-Fec1h24nflQdsBL0zwyGv4
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-22 10:04:16:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-22 10:04:16:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-29 10:04:16:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.rAWk09Tsxa9-iWLCrIuA-Fec1h24nflQdsBL0zwyGv4
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryx047vNuNArXHCFDJ
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 10096
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryx047vNuNArXHCFDJ
      ┣ 
      ┣ file (IFormFile)：               [name]: 导入记录-2025-07-21_110755.xlsx; [size]: 10KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:49.6320958 +08:00 Tuesday L System.Logging.StringLogging[0] #11 '00-bd7c0551f2145d2bc32a4415d7552364-c3ddbd497ae5b913-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:15:49——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE ((`staff_code` IN ('20250205','20250206')) AND( `IsDelete` = @IsDelete1 ))  AND ( `IsDelete` = @IsDelete2 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      [Name]:@IsDelete2 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:49.6581851 +08:00 Tuesday L System.Logging.UnitOfWork[0] #11 '00-bd7c0551f2145d2bc32a4415d7552364-c3ddbd497ae5b913-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:49.6795978 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #11 '00-bd7c0551f2145d2bc32a4415d7552364-c3ddbd497ae5b913-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 127.0.0.1
      ┣ 客户端源端口：                   57245
      ┣ 服务端 IP 地址：                 127.0.0.1
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-bd7c0551f2145d2bc32a4415d7552364-c3ddbd497ae5b913-00
      ┣ 服务线程 ID：                    #11
      ┣ 执行耗时：                       182ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.rAWk09Tsxa9-iWLCrIuA-Fec1h24nflQdsBL0zwyGv4
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-22 10:04:16:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-22 10:04:16:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-29 10:04:16:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.rAWk09Tsxa9-iWLCrIuA-Fec1h24nflQdsBL0zwyGv4
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundarym33W9rZwESiQmr7E
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 10096
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundarym33W9rZwESiQmr7E
      ┣ 
      ┣ file (IFormFile)：               [name]: 导入记录-2025-07-21_110755.xlsx; [size]: 10KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:50.6078340 +08:00 Tuesday L System.Logging.StringLogging[0] #36 '00-96b070b5b53bd98e418070376dc887f6-392a09e30e78c4d0-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:15:50——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE ((`staff_code` IN ('20250205','20250206')) AND( `IsDelete` = @IsDelete1 ))  AND ( `IsDelete` = @IsDelete2 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      [Name]:@IsDelete2 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:50.6365156 +08:00 Tuesday L System.Logging.UnitOfWork[0] #36 '00-96b070b5b53bd98e418070376dc887f6-392a09e30e78c4d0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:50.6593750 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #36 '00-96b070b5b53bd98e418070376dc887f6-392a09e30e78c4d0-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 127.0.0.1
      ┣ 客户端源端口：                   57245
      ┣ 服务端 IP 地址：                 127.0.0.1
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-96b070b5b53bd98e418070376dc887f6-392a09e30e78c4d0-00
      ┣ 服务线程 ID：                    #36
      ┣ 执行耗时：                       211ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.rAWk09Tsxa9-iWLCrIuA-Fec1h24nflQdsBL0zwyGv4
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-22 10:04:16:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-22 10:04:16:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-29 10:04:16:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.rAWk09Tsxa9-iWLCrIuA-Fec1h24nflQdsBL0zwyGv4
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryf9MmcctJILJUo5RP
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 10096
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryf9MmcctJILJUo5RP
      ┣ 
      ┣ file (IFormFile)：               [name]: 导入记录-2025-07-21_110755.xlsx; [size]: 10KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:51.6782749 +08:00 Tuesday L System.Logging.StringLogging[0] #21 '00-0aff91040e44076e4fe54b99763ea9d7-ba55c91c6a062764-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:15:51——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE ((`staff_code` IN ('20250205','20250206')) AND( `IsDelete` = @IsDelete1 ))  AND ( `IsDelete` = @IsDelete2 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      [Name]:@IsDelete2 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:51.7046254 +08:00 Tuesday L System.Logging.UnitOfWork[0] #21 '00-0aff91040e44076e4fe54b99763ea9d7-ba55c91c6a062764-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:15:51.7273307 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #21 '00-0aff91040e44076e4fe54b99763ea9d7-ba55c91c6a062764-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 127.0.0.1
      ┣ 客户端源端口：                   57245
      ┣ 服务端 IP 地址：                 127.0.0.1
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-0aff91040e44076e4fe54b99763ea9d7-ba55c91c6a062764-00
      ┣ 服务线程 ID：                    #21
      ┣ 执行耗时：                       201ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.rAWk09Tsxa9-iWLCrIuA-Fec1h24nflQdsBL0zwyGv4
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-22 10:04:16:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-22 10:04:16:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-29 10:04:16:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.rAWk09Tsxa9-iWLCrIuA-Fec1h24nflQdsBL0zwyGv4
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundarykStrxiFQ1xgKnALA
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 10096
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundarykStrxiFQ1xgKnALA
      ┣ 
      ┣ file (IFormFile)：               [name]: 导入记录-2025-07-21_110755.xlsx; [size]: 10KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
crit: 2025-07-22 10:17:56.7674809 +08:00 Tuesday L System.Logging.ScheduleService[0] #39
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Schedule hosted service is stopped.
crit: 2025-07-22 10:17:56.7738330 +08:00 Tuesday L System.Logging.TaskQueueService[0] #24
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(ILogger logger, string message, params object[] args)
      TaskQueue hosted service is stopped.
fail: 2025-07-22 10:19:52.8180490 +08:00 Tuesday L System.Logging.StringLogging[0] #28 '00-0f9ada82f3cddb6564f957016fc007e4-1f1f8233ce0b7ced-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:19:52——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE ((`staff_code` IN ('20250205','20250206')) AND( `IsDelete` = @IsDelete1 ))  AND ( `IsDelete` = @IsDelete2 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      [Name]:@IsDelete2 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:19:52.8512839 +08:00 Tuesday L System.Logging.UnitOfWork[0] #28 '00-0f9ada82f3cddb6564f957016fc007e4-1f1f8233ce0b7ced-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:19:52.8699291 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #28 '00-0f9ada82f3cddb6564f957016fc007e4-1f1f8233ce0b7ced-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36
      ┣ 客户端区域语言：                 zh-CN
      ┣ 客户端 IP 地址：                 127.0.0.1
      ┣ 客户端源端口：                   59782
      ┣ 服务端 IP 地址：                 127.0.0.1
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-0f9ada82f3cddb6564f957016fc007e4-1f1f8233ce0b7ced-00
      ┣ 服务线程 ID：                    #28
      ┣ 执行耗时：                       326ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.bGYi23aO8cKyblcvxmxQ2Iw06B09ucUpDTHj14YZs6Y
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-22 10:19:12:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-22 10:19:12:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-29 10:19:12:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.bGYi23aO8cKyblcvxmxQ2Iw06B09ucUpDTHj14YZs6Y
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundary8h4iWzvj4fofXRte
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 10096
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not A(Brand";v="8", "Chromium";v="132"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundary8h4iWzvj4fofXRte
      ┣ 
      ┣ file (IFormFile)：               [name]: 导入记录-2025-07-21_110755.xlsx; [size]: 10KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
crit: 2025-07-22 10:25:41.7837108 +08:00 Tuesday L System.Logging.ScheduleService[0] #21
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Schedule hosted service is stopped.
crit: 2025-07-22 10:25:41.8168792 +08:00 Tuesday L System.Logging.TaskQueueService[0] #25
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(ILogger logger, string message, params object[] args)
      TaskQueue hosted service is stopped.
fail: 2025-07-22 10:26:14.2225242 +08:00 Tuesday L System.Logging.StringLogging[0] #25 '00-eed71efc2d512634ab349f24a64687a9-0c2f1250b484f5b9-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:26:14——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE ((`staff_code` IN ('20250205','20250206')) AND( `IsDelete` = @IsDelete1 ))  AND ( `IsDelete` = @IsDelete2 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      [Name]:@IsDelete2 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:26:17.5444067 +08:00 Tuesday L System.Logging.StringLogging[0] #23 '00-130020326342bfb791e8caaedc6a9904-74a552c82656a023-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:26:17——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE ((`staff_code` IN ('20250205','20250206')) AND( `IsDelete` = @IsDelete1 ))  AND ( `IsDelete` = @IsDelete2 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      [Name]:@IsDelete2 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:26:17.5770050 +08:00 Tuesday L System.Logging.UnitOfWork[0] #23 '00-130020326342bfb791e8caaedc6a9904-74a552c82656a023-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 51
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:26:17.5966137 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #23 '00-130020326342bfb791e8caaedc6a9904-74a552c82656a023-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36
      ┣ 客户端区域语言：                 zh-CN
      ┣ 客户端 IP 地址：                 127.0.0.1
      ┣ 客户端源端口：                   50245
      ┣ 服务端 IP 地址：                 127.0.0.1
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-130020326342bfb791e8caaedc6a9904-74a552c82656a023-00
      ┣ 服务线程 ID：                    #23
      ┣ 执行耗时：                       211ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.bGYi23aO8cKyblcvxmxQ2Iw06B09ucUpDTHj14YZs6Y
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-22 10:19:12:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-22 10:19:12:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-29 10:19:12:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Trae/1.100.3 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.bGYi23aO8cKyblcvxmxQ2Iw06B09ucUpDTHj14YZs6Y
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundarymwxK7F4GNanOSePw
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 10096
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not A(Brand";v="8", "Chromium";v="132"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundarymwxK7F4GNanOSePw
      ┣ 
      ┣ file (IFormFile)：               [name]: 导入记录-2025-07-21_110755.xlsx; [size]: 10KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 51
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 51
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:31:47.6282950 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-e490713b25069390e164655f93ad4987-09f798b56141d321-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:31:47——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE ((`staff_code` IN ('20250205','20250206')) AND( `IsDelete` = @IsDelete1 ))  AND ( `IsDelete` = @IsDelete2 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      [Name]:@IsDelete2 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:31:47.6590523 +08:00 Tuesday L System.Logging.UnitOfWork[0] #14 '00-e490713b25069390e164655f93ad4987-09f798b56141d321-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:31:47.6878075 +08:00 Tuesday L System.Logging.LoggingMonitor[0] #14 '00-e490713b25069390e164655f93ad4987-09f798b56141d321-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 127.0.0.1
      ┣ 客户端源端口：                   62865
      ┣ 服务端 IP 地址：                 127.0.0.1
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-e490713b25069390e164655f93ad4987-09f798b56141d321-00
      ┣ 服务线程 ID：                    #14
      ┣ 执行耗时：                       349ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://[::]:5005
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       Admin.NET.Web.Entry
      ┣ 托管程序：                       Kestrel
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.rAWk09Tsxa9-iWLCrIuA-Fec1h24nflQdsBL0zwyGv4
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-22 10:04:16:0000(+08:00) Tuesday L)
      ┣ nbf (integer64)：                ********** (2025-07-22 10:04:16:0000(+08:00) Tuesday L)
      ┣ exp (integer64)：                ********** (2025-07-29 10:04:16:0000(+08:00) Tuesday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Connection：                     keep-alive
      ┣ Host：                           localhost:5005
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.rAWk09Tsxa9-iWLCrIuA-Fec1h24nflQdsBL0zwyGv4
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryhowjdBiAgBXHuIe4
      ┣ Origin：                         http://localhost:8888
      ┣ Referer：                        http://localhost:8888/
      ┣ Content-Length：                 10096
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryhowjdBiAgBXHuIe4
      ┣ 
      ┣ file (IFormFile)：               [name]: 导入记录-2025-07-21_110755.xlsx; [size]: 10KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
crit: 2025-07-22 10:32:58.1397161 +08:00 Tuesday L System.Logging.ScheduleService[0] #23
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Schedule hosted service is stopped.
crit: 2025-07-22 10:32:58.1438615 +08:00 Tuesday L System.Logging.TaskQueueService[0] #21
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(ILogger logger, string message, params object[] args)
      TaskQueue hosted service is stopped.
fail: 2025-07-22 10:34:23.7588427 +08:00 Tuesday L System.Logging.StringLogging[0] #25 '00-0dd2a0d3cd82772ea9c080ac3aa365f3-d884440268cb2431-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:34:23——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:34:23.471',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700360545505349'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:34:23.472',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700360545505350') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
crit: 2025-07-22 10:40:51.7658276 +08:00 Tuesday L System.Logging.ScheduleService[0] #39
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Schedule hosted service is stopped.
fail: 2025-07-22 10:41:46.9234832 +08:00 Tuesday L System.Logging.StringLogging[0] #27 '00-4064e93a7652a8c7d963208df21654b5-e776b25b16c81fa9-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:41:46——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:41:46.858',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700362361638981'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:41:46.861',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700362361638982') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:41:52.3735512 +08:00 Tuesday L System.Logging.StringLogging[0] #24 '00-0db7163db24fdbaa0f379898077026e4-e72aaeee37e81be4-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:41:52——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:41:52.318',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700362384117829'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:41:52.318',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700362384117830') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
crit: 2025-07-22 10:55:07.4232400 +08:00 Tuesday L System.Logging.ScheduleService[0] #39
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Schedule hosted service is stopped.
crit: 2025-07-22 10:55:07.4461039 +08:00 Tuesday L System.Logging.TaskQueueService[0] #25
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(ILogger logger, string message, params object[] args)
      TaskQueue hosted service is stopped.
fail: 2025-07-22 10:58:53.6474521 +08:00 Tuesday L System.Logging.StringLogging[0] #11 '00-33b7f44c822f54250fd7a845f5fffecf-10a6ba456073fbcf-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:58:53——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:58:53.563',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700366567022661'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:58:53.565',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700366567022662') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 10:59:54.3334139 +08:00 Tuesday L System.Logging.StringLogging[0] #22 '00-b71ded2bb309460abf741ef696bfa4de-2610bfbc04f8a917-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 10:59:54——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:59:54.276',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700366815842373'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 10:59:54.277',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700366815842374') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 11:03:45.8534092 +08:00 Tuesday L System.Logging.StringLogging[0] #11 '00-d8ddb4c5eccef7924c041a66c732455e-449455a921306d40-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 11:03:45——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 11:03:45.794',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700367764099141'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 11:03:45.795',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700367764099142') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 11:12:22.0783487 +08:00 Tuesday L System.Logging.StringLogging[0] #14 '00-2aab3278d4a4869613fb9c387c8b7c9f-4dedbe74185d9aa3-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 11:12:22——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 11:12:22.023',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700369878597701'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 11:12:22.023',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700369878597702') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-22 11:12:24.2661694 +08:00 Tuesday L System.Logging.StringLogging[0] #28 '00-77c9f60162d8ea7105cb9cf89e36da22-2849b452969615b7-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 11:12:24——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 11:12:24.210',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700369887551557'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 11:12:24.210',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700369887551558') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
crit: 2025-07-22 11:12:26.7871440 +08:00 Tuesday L System.Logging.ScheduleService[0] #3
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Schedule hosted service is stopped.
crit: 2025-07-22 11:12:26.7908427 +08:00 Tuesday L System.Logging.TaskQueueService[0] #12
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(ILogger logger, string message, params object[] args)
      TaskQueue hosted service is stopped.
fail: 2025-07-22 11:13:57.6092417 +08:00 Tuesday L System.Logging.StringLogging[0] #7 '00-b109cc0fc8f52715d9da4ad33d248295-5a6ba746563e695d-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-22 11:13:57——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 11:13:57.541',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700370269663301'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-22 11:13:57.543',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700370269663302') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
crit: 2025-07-22 11:18:45.5000695 +08:00 Tuesday L System.Logging.ScheduleService[0] #27
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Schedule hosted service is stopped.
crit: 2025-07-22 11:18:45.5093946 +08:00 Tuesday L System.Logging.TaskQueueService[0] #27
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(ILogger logger, string message, params object[] args)
      TaskQueue hosted service is stopped.
crit: 2025-07-22 11:23:56.1905561 +08:00 Tuesday L System.Logging.ScheduleService[0] #37
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.Log(LogLevel logLevel, string message, object[] args, Exception ex)
      Schedule hosted service is stopped.
crit: 2025-07-22 11:23:56.2031625 +08:00 Tuesday L System.Logging.TaskQueueService[0] #43
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogCritical(ILogger logger, string message, params object[] args)
      TaskQueue hosted service is stopped.
ption: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
       ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
         at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
         at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
         at System.Net.Sockets.Socket.Bind(EndPoint localEP)
         at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
         at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
         --- End of inner exception stack trace ---
         at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
         at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
         at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
         at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
      --- End of stack trace from previous location ---
         at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
         --- End of inner exception stack trace ---
         at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
         at Microsoft.AspNetCore.Server.Kestrel.Core.ListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
         at Microsoft.AspNetCore.Server.Kestrel.Core.AnyIPListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
         at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
         at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
         at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
         at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
         at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
         at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
         at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
crit: 2025-07-22 12:12:29.2828308 +08:00 Tuesday L System.Logging.ScheduleService[0] #11
      [Furion.Pure.dll] void Furion.Schedule.ScheduleLogger.LogCritical(string message, params object[] args)
      Schedule hosted service is stopped.
fail: 2025-07-22 14:22:23.9314354 +08:00 Tuesday L System.Logging.StringLogging[0] #24 '00-d2669b1dd34c593c44b18726307ba561-7080f3f398e65443-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      用户密码验证异常：      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.ArgumentException: Invalid point coordinates
         at Org.BouncyCastle.Math.EC.ECCurve.ValidatePoint(BigInteger x, BigInteger y) in D:\a\1\s\crypto\src\math\ec\ECCurve.cs:line 112
         at Org.BouncyCastle.Math.EC.ECCurve.DecodePoint(Byte[] encoded) in D:\a\1\s\crypto\src\math\ec\ECCurve.cs:line 493
         at Org.BouncyCastle.Crypto.Engines.SM2Engine.Decrypt(Byte[] input, Int32 inOff, Int32 inLen) in D:\a\1\s\crypto\src\crypto\engines\SM2Engine.cs:line 119
         at Org.BouncyCastle.Crypto.Engines.SM2Engine.ProcessBlock(Byte[] input, Int32 inOff, Int32 inLen) in D:\a\1\s\crypto\src\crypto\engines\SM2Engine.cs:line 71
         at Admin.NET.Core.GM.Sm2DecryptOld(Byte[] data, AsymmetricKeyParameter key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Utils\GM\GM.cs:line 181
         at Admin.NET.Core.GM.Sm2Decrypt(Byte[] data, AsymmetricKeyParameter key) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Utils\GM\GM.cs:line 141
         at Admin.NET.Core.GMUtil.SM2Decrypt(String privateKey_string, String encryptedData_string) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Utils\GM\GMUtil.cs:line 59
         at Admin.NET.Core.CryptogramUtil.SM2Decrypt(String cipherText) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Utils\CryptogramUtil.cs:line 78
         at Admin.NET.Core.Service.SysAuthService.VerifyPassword(String password, String keyPasswordErrorTimes, Int32 passwordErrorTimes, SysUser user) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\Service\Auth\SysAuthService.cs:line 151
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
