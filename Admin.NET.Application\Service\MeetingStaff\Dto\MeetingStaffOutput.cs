﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！
using Magicodes.ExporterAndImporter.Core;
namespace Admin.NET.Application;

/// <summary>
/// 会议人员表输出参数
/// </summary>
public class MeetingStaffOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 平台员工ID
    /// </summary>
    public int? staff_id { get; set; }    
    
    /// <summary>
    /// 员工编号
    /// </summary>
    public string? staff_code { get; set; }    
    
    /// <summary>
    /// 员工姓名
    /// </summary>
    public string? staff_name { get; set; }    
    
    /// <summary>
    /// 职位
    /// </summary>
    public string? position { get; set; }    
    
    /// <summary>
    /// 部门
    /// </summary>
    public string? department { get; set; }    
    
    /// <summary>
    /// 邮箱
    /// </summary>
    public string? email { get; set; }    
    
    /// <summary>
    /// 手机号
    /// </summary>
    public string? phone { get; set; }    
    
    /// <summary>
    /// 头像
    /// </summary>
    public string? avatar_url { get; set; }    
    
    /// <summary>
    /// 绑定会议室ID
    /// </summary>
    public long? meeting_room_id { get; set; }    
    
    /// <summary>
    /// 自定义字段1
    /// </summary>
    public string? field1 { get; set; }    
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    public string? field2 { get; set; }    
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    public string? field3 { get; set; }    
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    public string? field4 { get; set; }    
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    public string? field5 { get; set; }    
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    public string? field6 { get; set; }    
    
    /// <summary>
    /// 描述
    /// </summary>
    public string? description { get; set; }    
    
    /// <summary>
    /// 1：激活；2：未激活
    /// </summary>
    public int? meeting_staff_status { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
}

/// <summary>
/// 会议人员表数据导入模板实体
/// </summary>
public class ExportMeetingStaffOutput : ImportMeetingStaffInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
