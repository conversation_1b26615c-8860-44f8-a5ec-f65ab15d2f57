fail: 2025-07-21 11:07:54.0150199 +08:00 Monday L System.Logging.StringLogging[0] #26 '00-2f3d7a90053ec302c7800cec49e9bd2c-95edd620659afc1b-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 11:07:53——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE  (`staff_code` IN ('20250205','20250206'))   AND ( `IsDelete` = @IsDelete1 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 11:07:54.7913461 +08:00 Monday L Admin.NET.Application.MeetingStaffService[0] #26 '00-2f3d7a90053ec302c7800cec49e9bd2c-95edd620659afc1b-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 344
         at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 278
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 387
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 11:34:48.4491460 +08:00 Monday L System.Logging.StringLogging[0] #40 '00-99ed1ed0ab148965cd9a40bb48089500-93a72704dfe7a7e5-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 11:34:48——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE  (`staff_code` IN ('20250205','20250206'))   AND ( `IsDelete` = @IsDelete1 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 11:34:50.0704758 +08:00 Monday L Admin.NET.Application.MeetingStaffService[0] #40 '00-99ed1ed0ab148965cd9a40bb48089500-93a72704dfe7a7e5-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 344
         at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 278
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 387
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 11:51:01.7492413 +08:00 Monday L System.Logging.StringLogging[0] #10 '00-5f2e5c83e1b4b5952e06a2679c5c529c-628fab766699e724-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 11:51:01——错误SQL】
      
      [Sql]:SELECT `staff_code` FROM `meeting_staff`  WHERE  (`staff_code` IN ('20250205','20250206'))   AND ( `IsDelete` = @IsDelete1 )  
      [Pars]:
      [Name]:@IsDelete1 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 11:51:02.0740016 +08:00 Monday L Admin.NET.Application.MeetingStaffService[0] #10 '00-5f2e5c83e1b4b5952e06a2679c5c529c-628fab766699e724-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 344
         at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 278
         at SqlSugar.AdoProvider.GetDataReader(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetData[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToList[TResult]()
         at SqlSugar.QueryableProvider`1.ToList()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 387
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 14:13:29.9041221 +08:00 Monday L System.Logging.StringLogging[0] #24 '00-4f6c0d4e3e928feeca8721acb09197fe-c4d64d79c83c8e81-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 14:13:29——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,@avatar_url_7,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 14:13:27.118',NULL,N'*************',@CreateUserName_8,NULL,NULL,N'700060488069189'),  (NULL,@staff_code_9,@staff_name_10,@position_11,@department_12,@email_13,@phone_14,@avatar_url_15,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 14:13:27.169',NULL,N'*************',@CreateUserName_16,NULL,NULL,N'700060488290373') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_7 [Value]: [Type]:String    
      [Name]:@CreateUserName_8 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_9 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_10 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_11 [Value]:测试 [Type]:String    
      [Name]:@department_12 [Value]:测试 [Type]:String    
      [Name]:@email_13 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_14 [Value]:13546947512 [Type]:String    
      [Name]:@avatar_url_15 [Value]: [Type]:String    
      [Name]:@CreateUserName_16 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 14:13:31.8432917 +08:00 Monday L Admin.NET.Application.MeetingStaffService[0] #24 '00-4f6c0d4e3e928feeca8721acb09197fe-c4d64d79c83c8e81-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      批量处理导入数据时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
         at SqlSugar.AdoProvider.ExecuteCommand(String sql, SugarParameter[] parameters)
         at SqlSugar.InsertableProvider`1.ExecuteCommand()
         at Admin.NET.Application.MeetingStaffService.<>c__DisplayClass18_0.<ImportData>b__1(List`1 pageItems) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\MeetingStaff\MeetingStaffService.cs:line 481
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 16:27:24.7988251 +08:00 Monday L System.Logging.StringLogging[0] #23 '00-87d9173804e62e702c3801eaedfe8c21-4a5382a415244408-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 16:27:24——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 16:27:24.265','2025-07-21 16:27:24.265',N'*************',@CreateUserName_7,N'*************',@UpdateUserName_8,N'700093407977541'),  (NULL,@staff_code_9,@staff_name_10,@position_11,@department_12,@email_13,@phone_14,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 16:27:24.266','2025-07-21 16:27:24.266',N'*************',@CreateUserName_15,N'*************',@UpdateUserName_16,N'700093407977542') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@UpdateUserName_8 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_9 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_10 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_11 [Value]:测试 [Type]:String    
      [Name]:@department_12 [Value]:测试 [Type]:String    
      [Name]:@email_13 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_14 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_15 [Value]:超级管理员 [Type]:String    
      [Name]:@UpdateUserName_16 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 16:58:23.9058215 +08:00 Monday L System.Logging.StringLogging[0] #40 '00-610fb9f84afee3a142525229b32f5653-9f3ef6aff36f3223-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 16:58:23——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 16:58:23.531','2025-07-21 16:58:23.531',N'*************',@CreateUserName_7,N'*************',@UpdateUserName_8,N'700101023498309'),  (NULL,@staff_code_9,@staff_name_10,@position_11,@department_12,@email_13,@phone_14,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 16:58:23.534','2025-07-21 16:58:23.534',N'*************',@CreateUserName_15,N'*************',@UpdateUserName_16,N'700101023502405') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@UpdateUserName_8 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_9 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_10 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_11 [Value]:测试 [Type]:String    
      [Name]:@department_12 [Value]:测试 [Type]:String    
      [Name]:@email_13 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_14 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_15 [Value]:超级管理员 [Type]:String    
      [Name]:@UpdateUserName_16 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 17:27:08.9523172 +08:00 Monday L System.Logging.UnitOfWork[0] #46 '00-9a80301d69cf985fc81684552f0e995c-5a5f10f13ceb168b-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      [Database Transaction] Transaction rolled back due to an error.      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 17:27:09.2026158 +08:00 Monday L System.Logging.LoggingMonitor[0] #46 '00-9a80301d69cf985fc81684552f0e995c-5a5f10f13ceb168b-00'
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      ┏━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━
      ┣ Admin.NET.Application.MeetingStaffService.ImportData
      ┣ 
      ┣ 控制器名称：                     MeetingStaffService
      ┣ 操作名称：                       ImportData
      ┣ 显示名称：                       导入会议人员表记录
      ┣ 路由信息：                       [area]: ; [controller]: meetingStaff; [action]: import
      ┣ 请求方式：                       POST
      ┣ 请求地址：                       http://localhost:5005/api/meetingStaff/import
      ┣ HTTP 协议：                      HTTP/1.1
      ┣ 来源地址：                       http://localhost:8888/
      ┣ 请求端源：                       client
      ┣ 浏览器标识：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ 客户端区域语言：                 zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ 客户端 IP 地址：                 *******
      ┣ 客户端源端口：                   58209
      ┣ 服务端 IP 地址：                 *******
      ┣ 服务端源端口：                   5005
      ┣ 客户端连接 ID：                  00-9a80301d69cf985fc81684552f0e995c-5a5f10f13ceb168b-00
      ┣ 服务线程 ID：                    #46
      ┣ 执行耗时：                       16322ms
      ┣ ━━━━━━━━━━━━━━━  Cookies ━━━━━━━━━━━━━━━
      ┣ 请求端：                         
      ┣ 响应端：                         
      ┣ ━━━━━━━━━━━━━━━  系统信息 ━━━━━━━━━━━━━━━
      ┣ 系统名称：                       Microsoft Windows 10.0.26100
      ┣ 系统架构：                       X64
      ┣ 基础框架：                       Furion.Pure v4.9.7.93
      ┣ .NET 架构：                      .NET 8.0.17
      ┣ ━━━━━━━━━━━━━━━  启动信息 ━━━━━━━━━━━━━━━
      ┣ Web 启动地址：                   http://localhost:5005/
      ┣ 运行环境：                       Development
      ┣ 启动程序集：                     Admin.NET.Web.Entry
      ┣ 进程名称：                       iisexpress
      ┣ 托管程序：                       iisexpress
      ┣ ━━━━━━━━━━━━━━━  授权信息 ━━━━━━━━━━━━━━━
      ┣ JWT Token：                      Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.ujWZs3u35rLBADMaJcteqGcHLJWndNIuK2FChV50HAc
      ┣ 
      ┣ UserId (integer64)：             *************
      ┣ TenantId (integer64)：           *************
      ┣ Account (string)：               superadmin
      ┣ RealName (string)：              超级管理员
      ┣ AccountType (integer32)：        999
      ┣ OrgId (integer32)：              0
      ┣ OrgName (JSON_NULL)：            
      ┣ OrgType (JSON_NULL)：            
      ┣ iat (integer64)：                ********** (2025-07-21 17:25:40:0000(+08:00) Monday L)
      ┣ nbf (integer64)：                ********** (2025-07-21 17:25:40:0000(+08:00) Monday L)
      ┣ exp (integer64)：                ********** (2025-07-28 17:25:40:0000(+08:00) Monday L)
      ┣ iss (string)：                   Admin.NET
      ┣ aud (string)：                   Admin.NET
      ┣ ━━━━━━━━━━━━━━━  请求头信息 ━━━━━━━━━━━━━━━
      ┣ 
      ┣ Accept：                         application/json, text/plain, */*
      ┣ Accept-Encoding：                gzip, deflate, br, zstd
      ┣ Accept-Language：                zh-CN,zh;q=0.9,en-GB;q=0.8,en-US;q=0.7,en;q=0.6,zh-HK;q=0.5
      ┣ Authorization：                  Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************.ujWZs3u35rLBADMaJcteqGcHLJWndNIuK2FChV50HAc
      ┣ Connection：                     keep-alive
      ┣ Content-Length：                 10096
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryJKyBfHaevKBrcNAp
      ┣ Host：                           localhost:5005
      ┣ Referer：                        http://localhost:8888/
      ┣ User-Agent：                     Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
      ┣ sec-ch-ua-platform：             "Windows"
      ┣ sec-ch-ua：                      "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"
      ┣ sec-ch-ua-mobile：               ?0
      ┣ Origin：                         http://localhost:8888
      ┣ Sec-Fetch-Site：                 same-site
      ┣ Sec-Fetch-Mode：                 cors
      ┣ Sec-Fetch-Dest：                 empty
      ┣ ━━━━━━━━━━━━━━━  参数列表 ━━━━━━━━━━━━━━━
      ┣ Content-Type：                   multipart/form-data; boundary=----WebKitFormBoundaryJKyBfHaevKBrcNAp
      ┣ 
      ┣ file (IFormFile)：               [name]: 导入记录-2025-07-21_110755.xlsx; [size]: 10KB; [content-type]: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      ┣ ━━━━━━━━━━━━━━━  返回信息 ━━━━━━━━━━━━━━━
      ┣ HTTP响应状态码：                 200
      ┣ 原始类型：                       Microsoft.AspNetCore.Mvc.IActionResult
      ┣ 最终类型：                       
      ┣ 最终返回值：                     null
      ┣ ━━━━━━━━━━━━━━━  异常信息 ━━━━━━━━━━━━━━━
      ┣ 类型：                           System.InvalidOperationException
      ┣ 消息：                           This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ┣ 错误堆栈：                       at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ┗━━━━━━━━━━━  Logging Monitor ━━━━━━━━━━━      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at MySqlConnector.Core.ServerSession.StartQuerying(ICancellableCommand command) in /_/src/MySqlConnector/Core/ServerSession.cs:line 283
         at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(IReadOnlyList`1 commands, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken)
         at MySqlConnector.MySqlCommand.ExecuteNonQueryAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 296
         at MySqlConnector.MySqlTransaction.CommitAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlTransaction.cs:line 36
         at MySqlConnector.MySqlTransaction.Commit() in /_/src/MySqlConnector/MySqlTransaction.cs:line 15
         at SqlSugar.AdoProvider.CommitTran()
         at SqlSugar.SqlSugarClient.CommitTran()
         at Admin.NET.Core.SqlSugarUnitOfWork.CommitTransaction(FilterContext resultContext, UnitOfWorkAttribute unitOfWork) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Core\SqlSugar\SqlSugarUnitOfWork.cs:line 47
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.CommitTransaction(FilterContext context, IUnitOfWork _unitOfWork, UnitOfWorkAttribute unitOfWorkAttribute, FilterContext resultContext)
         at Furion.DatabaseAccessor.UnitOfWorkAttribute.OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
         at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 17:29:04.8360308 +08:00 Monday L System.Logging.StringLogging[0] #48 '00-9ef00a5c0c2819f6b7c0f56198a0e75e-84c771509b4f37be-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 17:29:04——错误SQL】
      
      [Sql]:SELECT `staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `meeting_staff`  WHERE ( (   `Id` = @Condit_Id_1000   )  OR (   `Id` = @Condit_Id_1001   ) )   AND ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@Condit_Id_1000 [Value]:700108564807749 [Type]:Int64    
      [Name]:@Condit_Id_1001 [Value]:700108564807750 [Type]:Int64    
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 17:30:11.3609760 +08:00 Monday L System.Logging.StringLogging[0] #27 '00-74e148fc34a13b2acb723313acf74d9e-98f448841568105a-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 17:30:11——错误SQL】
      
      [Sql]:SELECT `staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `meeting_staff`  WHERE ( (   `Id` = @Condit_Id_1000   )  OR (   `Id` = @Condit_Id_1001   ) )   AND ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@Condit_Id_1000 [Value]:700108837470277 [Type]:Int64    
      [Name]:@Condit_Id_1001 [Value]:700108837470278 [Type]:Int64    
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 17:40:42.6703144 +08:00 Monday L System.Logging.StringLogging[0] #3 '00-79ebb84d5ff8ed5c8824b6bba19736f7-7031b77e1cd94807-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 17:40:42——错误SQL】
      
      [Sql]:SELECT `staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `meeting_staff`  WHERE ( (   `Id` = @Condit_Id_1000   )  OR (   `Id` = @Condit_Id_1001   ) )   AND ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@Condit_Id_1000 [Value]:700111423250501 [Type]:Int64    
      [Name]:@Condit_Id_1001 [Value]:700111423250502 [Type]:Int64    
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 17:41:00.0726270 +08:00 Monday L System.Logging.StringLogging[0] #3 '00-1407386758e0991c4ad88bf751d45d8c-ae85972dd71911f0-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 17:40:59——错误SQL】
      
      [Sql]:SELECT `staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `meeting_staff`  WHERE ( (   `Id` = @Condit_Id_1000   )  OR (   `Id` = @Condit_Id_1001   ) )   AND ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@Condit_Id_1000 [Value]:700111494905925 [Type]:Int64    
      [Name]:@Condit_Id_1001 [Value]:700111494905926 [Type]:Int64    
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 17:43:16.9455874 +08:00 Monday L System.Logging.StringLogging[0] #28 '00-6a9271759b4bb72c6bc538262923a1a3-345bfc5b0a8d5917-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 17:43:16——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 17:43:16.773',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700112054259781'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 17:43:16.776',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700112054259782') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-21 17:47:56.4039379 +08:00 Monday L System.Logging.StringLogging[0] #25 '00-11a9721c7c55fee722c6381355edc2ef-f0453bbf933de256-00'
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-21 17:47:55——错误SQL】
      
      [Sql]:INSERT INTO `meeting_staff`  (`staff_id`,`staff_code`,`staff_name`,`position`,`department`,`email`,`phone`,`avatar_url`,`meeting_room_id`,`field1`,`field2`,`field3`,`field4`,`field5`,`field6`,`description`,`meeting_staff_status`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id`) VALUES(NULL,@staff_code_1,@staff_name_2,@position_3,@department_4,@email_5,@phone_6,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 17:47:55.669',NULL,N'*************',@CreateUserName_7,NULL,NULL,N'700113196855365'),  (NULL,@staff_code_8,@staff_name_9,@position_10,@department_11,@email_12,@phone_13,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,N'1',0,'2025-07-21 17:47:55.677',NULL,N'*************',@CreateUserName_14,NULL,NULL,N'700113196855366') ;select @@IDENTITY
       
      [Pars]:
      [Name]:@staff_code_1 [Value]:20250205 [Type]:String    
      [Name]:@staff_name_2 [Value]:钟奥键 [Type]:String    
      [Name]:@position_3 [Value]:测试 [Type]:String    
      [Name]:@department_4 [Value]:测试 [Type]:String    
      [Name]:@email_5 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_6 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_7 [Value]:超级管理员 [Type]:String    
      [Name]:@staff_code_8 [Value]:20250206 [Type]:String    
      [Name]:@staff_name_9 [Value]:任瑞颖 [Type]:String    
      [Name]:@position_10 [Value]:测试 [Type]:String    
      [Name]:@department_11 [Value]:测试 [Type]:String    
      [Name]:@email_12 [Value]:<EMAIL> [Type]:String    
      [Name]:@phone_13 [Value]:13546947512 [Type]:String    
      [Name]:@CreateUserName_14 [Value]:超级管理员 [Type]:String    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Duplicate entry '20250205' for key 'meeting_staff.Index_meeting_staff_staff_code_Unique'
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
