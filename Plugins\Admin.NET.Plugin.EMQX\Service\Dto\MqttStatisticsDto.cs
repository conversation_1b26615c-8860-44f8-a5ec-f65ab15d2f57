// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// MQTT统计信息DTO
/// </summary>
public class MqttStatisticsDto
{
    /// <summary>
    /// 总连接数
    /// </summary>
    public int TotalConnections { get; set; }

    /// <summary>
    /// 在线连接数
    /// </summary>
    public int OnlineConnections { get; set; }

    /// <summary>
    /// 离线连接数
    /// </summary>
    public int OfflineConnections { get; set; }

    /// <summary>
    /// 连接成功率(%)
    /// </summary>
    public decimal ConnectionSuccessRate { get; set; }

    /// <summary>
    /// 总订阅数
    /// </summary>
    public int TotalSubscriptions { get; set; }

    /// <summary>
    /// 活跃订阅数
    /// </summary>
    public int ActiveSubscriptions { get; set; }

    /// <summary>
    /// 今日发送消息数
    /// </summary>
    public long TodaySentMessages { get; set; }

    /// <summary>
    /// 今日接收消息数
    /// </summary>
    public long TodayReceivedMessages { get; set; }

    /// <summary>
    /// 总发送消息数
    /// </summary>
    public long TotalSentMessages { get; set; }

    /// <summary>
    /// 总接收消息数
    /// </summary>
    public long TotalReceivedMessages { get; set; }

    /// <summary>
    /// 平均消息大小(字节)
    /// </summary>
    public decimal AverageMessageSize { get; set; }

    /// <summary>
    /// 服务器状态
    /// </summary>
    public string ServerStatus { get; set; }

    /// <summary>
    /// 服务器启动时间
    /// </summary>
    public DateTime? ServerStartTime { get; set; }

    /// <summary>
    /// 服务器运行时长(秒)
    /// </summary>
    public long ServerUptime { get; set; }

    /// <summary>
    /// 内存使用量(MB)
    /// </summary>
    public decimal MemoryUsage { get; set; }

    /// <summary>
    /// CPU使用率(%)
    /// </summary>
    public decimal CpuUsage { get; set; }
}

/// <summary>
/// MQTT流量统计DTO
/// </summary>
public class MqttTrafficDto
{
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 发送消息数
    /// </summary>
    public long SentMessages { get; set; }

    /// <summary>
    /// 接收消息数
    /// </summary>
    public long ReceivedMessages { get; set; }

    /// <summary>
    /// 发送字节数
    /// </summary>
    public long SentBytes { get; set; }

    /// <summary>
    /// 接收字节数
    /// </summary>
    public long ReceivedBytes { get; set; }

    /// <summary>
    /// 在线客户端数
    /// </summary>
    public int OnlineClients { get; set; }
}

/// <summary>
/// MQTT主题统计DTO
/// </summary>
public class MqttTopicStatisticsDto
{
    /// <summary>
    /// 主题名称
    /// </summary>
    public string Topic { get; set; }

    /// <summary>
    /// 订阅者数量
    /// </summary>
    public int SubscriberCount { get; set; }

    /// <summary>
    /// 消息数量
    /// </summary>
    public long MessageCount { get; set; }

    /// <summary>
    /// 最后消息时间
    /// </summary>
    public DateTime? LastMessageTime { get; set; }

    /// <summary>
    /// 平均消息大小(字节)
    /// </summary>
    public decimal AverageMessageSize { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public MqttMessageTypeEnum MessageType { get; set; }
}