// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// 阿里云MQTT签名工具类
/// </summary>
public static class AliyunMqttSignatureUtil
{
    /// <summary>
    /// 生成阿里云MQTT客户端ID
    /// 格式: GroupID@@@ClientID
    /// </summary>
    /// <param name="groupId">组ID</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>完整的客户端ID</returns>
    public static string GenerateClientId(string groupId, string clientId)
    {
        if (string.IsNullOrEmpty(groupId))
            throw new ArgumentException("组ID不能为空", nameof(groupId));
        if (string.IsNullOrEmpty(clientId))
            throw new ArgumentException("客户端ID不能为空", nameof(clientId));

        return $"{groupId}{EMQXConst.CLIENT_ID_SEPARATOR}{clientId}";
    }

    /// <summary>
    /// 生成阿里云MQTT用户名
    /// 格式: Signature|AccessKeyId|InstanceId
    /// </summary>
    /// <param name="accessKeyId">AccessKey ID</param>
    /// <param name="instanceId">实例ID</param>
    /// <returns>用户名</returns>
    public static string GenerateUsername(string accessKeyId, string instanceId)
    {
        if (string.IsNullOrEmpty(accessKeyId))
            throw new ArgumentException("AccessKey ID不能为空", nameof(accessKeyId));
        if (string.IsNullOrEmpty(instanceId))
            throw new ArgumentException("实例ID不能为空", nameof(instanceId));

        return $"Signature{EMQXConst.USERNAME_SEPARATOR}{accessKeyId}{EMQXConst.USERNAME_SEPARATOR}{instanceId}";
    }

    /// <summary>
    /// 生成阿里云MQTT密码(基于HMAC-SHA1签名算法)
    /// </summary>
    /// <param name="accessKeySecret">AccessKey Secret</param>
    /// <param name="clientId">客户端ID</param>
    /// <returns>签名密码</returns>
    public static string GeneratePassword(string accessKeySecret, string clientId)
    {
        if (string.IsNullOrEmpty(accessKeySecret))
            throw new ArgumentException("AccessKey Secret不能为空", nameof(accessKeySecret));
        if (string.IsNullOrEmpty(clientId))
            throw new ArgumentException("客户端ID不能为空", nameof(clientId));

        // 构建签名字符串
        var signString = BuildSignString(clientId);
        
        // 使用HMAC-SHA1算法生成签名
        return ComputeHmacSha1(signString, accessKeySecret);
    }

    /// <summary>
    /// 构建签名字符串
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>签名字符串</returns>
    private static string BuildSignString(string clientId)
    {
        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
        var nonce = Guid.NewGuid().ToString("N")[..16]; // 取前16位作为随机数
        
        var parameters = new SortedDictionary<string, string>
        {
            { "clientId", clientId },
            { "timestamp", timestamp },
            { "nonce", nonce },
            { "signatureMethod", EMQXConst.SIGNATURE_METHOD },
            { "signatureVersion", EMQXConst.SIGNATURE_VERSION }
        };

        var queryString = string.Join("&", parameters.Select(p => $"{p.Key}={p.Value}"));
        return $"POST\n\n\n{queryString}";
    }

    /// <summary>
    /// 计算HMAC-SHA1签名
    /// </summary>
    /// <param name="data">待签名数据</param>
    /// <param name="key">签名密钥</param>
    /// <returns>Base64编码的签名结果</returns>
    private static string ComputeHmacSha1(string data, string key)
    {
        var keyBytes = Encoding.UTF8.GetBytes(key);
        var dataBytes = Encoding.UTF8.GetBytes(data);
        
        using var hmac = new HMACSHA1(keyBytes);
        var hashBytes = hmac.ComputeHash(dataBytes);
        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// 验证阿里云MQTT认证信息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <param name="accessKeySecret">AccessKey Secret</param>
    /// <returns>验证结果</returns>
    public static bool ValidateCredentials(string clientId, string username, string password, string accessKeySecret)
    {
        try
        {
            // 解析用户名获取AccessKeyId和InstanceId
            var usernameParts = username.Split(EMQXConst.USERNAME_SEPARATOR);
            if (usernameParts.Length != 3 || usernameParts[0] != "Signature")
                return false;

            // 解析客户端ID获取GroupId和ClientId
            var clientIdParts = clientId.Split(EMQXConst.CLIENT_ID_SEPARATOR);
            if (clientIdParts.Length != 2)
                return false;

            // 重新生成密码进行验证
            var expectedPassword = GeneratePassword(accessKeySecret, clientId);
            return password == expectedPassword;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 解析客户端ID获取组ID和设备ID
    /// </summary>
    /// <param name="clientId">完整的客户端ID</param>
    /// <returns>组ID和设备ID的元组</returns>
    public static (string GroupId, string DeviceId) ParseClientId(string clientId)
    {
        if (string.IsNullOrEmpty(clientId))
            throw new ArgumentException("客户端ID不能为空", nameof(clientId));

        var parts = clientId.Split(EMQXConst.CLIENT_ID_SEPARATOR);
        if (parts.Length != 2)
            throw new ArgumentException("客户端ID格式不正确，应为GroupID@@@ClientID", nameof(clientId));

        return (parts[0], parts[1]);
    }

    /// <summary>
    /// 解析用户名获取AccessKeyId和InstanceId
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>AccessKeyId和InstanceId的元组</returns>
    public static (string AccessKeyId, string InstanceId) ParseUsername(string username)
    {
        if (string.IsNullOrEmpty(username))
            throw new ArgumentException("用户名不能为空", nameof(username));

        var parts = username.Split(EMQXConst.USERNAME_SEPARATOR);
        if (parts.Length != 3 || parts[0] != "Signature")
            throw new ArgumentException("用户名格式不正确，应为Signature|AccessKeyId|InstanceId", nameof(username));

        return (parts[1], parts[2]);
    }
}