﻿// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using Admin.NET.Plugin.GreenDisplay.Service;
using Microsoft.Extensions.Logging;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;
namespace Admin.NET.Application;

/// <summary>
/// 会议人员表服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class MeetingStaffService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MeetingStaff> _meetingStaffRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly GreenDisplayService _greenDisplayService;
    private readonly ILogger<MeetingStaffService> _logger;
    /// <summary>
    /// 
    /// </summary>
    /// <param name="meetingStaffRep"></param>
    /// <param name="sqlSugarClient"></param>
    /// <param name="greenDisplayService"></param>
    /// <param name="logger"></param>
    public MeetingStaffService(SqlSugarRepository<MeetingStaff> meetingStaffRep, ISqlSugarClient sqlSugarClient, GreenDisplayService greenDisplayService, ILogger<MeetingStaffService> logger)
    {
        _meetingStaffRep = meetingStaffRep;
        _sqlSugarClient = sqlSugarClient;
        _greenDisplayService = greenDisplayService;
        _logger = logger;
    }

    /// <summary>
    /// 分页查询会议人员表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询会议人员表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<MeetingStaffOutput>> Page(PageMeetingStaffInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _meetingStaffRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.staff_code.Contains(input.Keyword) || u.staff_name.Contains(input.Keyword) || u.position.Contains(input.Keyword) || u.department.Contains(input.Keyword) || u.email.Contains(input.Keyword) || u.phone.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.staff_code), u => u.staff_code.Contains(input.staff_code.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.staff_name), u => u.staff_name.Contains(input.staff_name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.position), u => u.position.Contains(input.position.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.department), u => u.department.Contains(input.department.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.email), u => u.email.Contains(input.email.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.phone), u => u.phone.Contains(input.phone.Trim()))
            .WhereIF(input.staff_id != null, u => u.staff_id == input.staff_id)
            .Select<MeetingStaffOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取会议人员表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取会议人员表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<MeetingStaff> Detail([FromQuery] QueryByIdMeetingStaffInput input)
    {
        return await _meetingStaffRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加会议人员表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加会议人员表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddMeetingStaffInput input)
    {
        // 使用事务确保数据一致性
        var result = await _sqlSugarClient.Ado.UseTranAsync(async () =>
        {
            var entity = input.Adapt<MeetingStaff>();

            // 检查staff_code在本地数据库中的唯一性
            if (!string.IsNullOrWhiteSpace(entity.staff_code))
            {
                var existingStaff = await _meetingStaffRep.GetFirstAsync(u => u.staff_code == entity.staff_code && u.IsDelete == false);
                if (existingStaff != null)
                {
                    throw Oops.Oh($"员工编号 '{entity.staff_code}' 已存在，请使用其他编号");
                }
            }

            // 先同步到第三方平台
            var thirdPartyInput = new CreateMeetingStaffInput
            {
                Code = entity.staff_code,
                Name = entity.staff_name,
                Mobile = entity.phone,
                Company = entity.department,
                Position = entity.position,
                Field1 = entity.field1,
                Field2 = entity.field2,
                Field3 = entity.field3,
                Field4 = entity.field4,
                Field5 = entity.field5,
                Field6 = entity.field6,
                Description = entity.description
            };

            var thirdPartyId = await _greenDisplayService.CreateMeetingStaffAsync(thirdPartyInput);

            

            // 第三方平台创建成功，回填staff_id并保存到本地数据库
            entity.staff_id = thirdPartyId;
            var insertResult = await _meetingStaffRep.InsertAsync(entity) ? entity.Id : 0;

            

            return insertResult;
        });

        return result.Data;
    }

    /// <summary>
    /// 更新会议人员表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新会议人员表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateMeetingStaffInput input)
    {
        // 使用事务确保数据一致性
        await _sqlSugarClient.Ado.UseTranAsync(async () =>
        {
            var entity = input.Adapt<MeetingStaff>();

            // 检查staff_code在本地数据库中的唯一性（排除当前记录）
            if (!string.IsNullOrWhiteSpace(entity.staff_code))
            {
                var existingStaff = await _meetingStaffRep.GetFirstAsync(u => u.staff_code == entity.staff_code && u.Id != entity.Id && u.IsDelete == false);
                if (existingStaff != null)
                {
                    throw Oops.Oh($"员工编号 '{entity.staff_code}' 已存在，请使用其他编号");
                }
            }
            await _meetingStaffRep.AsUpdateable(entity)
            .IgnoreColumns(u => new
            {
                u.meeting_staff_status,
            }).ExecuteCommandAsync();

            // 同步到第三方平台
            var thirdPartyInput = new Admin.NET.Plugin.GreenDisplay.Service.UpdateMeetingStaffInput
            {
                Code = entity.staff_code,
                Name = entity.staff_name,
                Mobile = entity.phone,
                Company = entity.department,
                Position = entity.position,
                Field1 = entity.field1,
                Field2 = entity.field2,
                Field3 = entity.field3,
                Field4 = entity.field4,
                Field5 = entity.field5,
                Field6 = entity.field6,
                Description = entity.description
            };
            var syncResult = await _greenDisplayService.UpdateMeetingStaffAsync(thirdPartyInput);
        });
    }

    /// <summary>
    /// 删除会议人员表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除会议人员表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteMeetingStaffInput input)
    {
        var entity = await _meetingStaffRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);

        // 使用事务确保数据一致性
        var result = await _sqlSugarClient.Ado.UseTranAsync(async () =>
        {
            // 先同步删除第三方平台数据
            if (!string.IsNullOrWhiteSpace(entity.staff_code) && entity.staff_id.HasValue)
            {
                var syncResult = await _greenDisplayService.DeleteMeetingStaffAsync(entity.staff_id.Value);
                if (!syncResult)
                {
                    _logger.LogError("会议人员删除同步到第三方平台失败 - ID: {Id}, 员工编号: {StaffCode}, 姓名: {Name}",
                        entity.Id, entity.staff_code, entity.staff_name);
                    throw Oops.Oh("同步删除第三方平台数据失败");
                }
                else
                {
                    _logger.LogInformation("会议人员删除同步到第三方平台成功 - ID: {Id}, 员工编号: {StaffCode}, 姓名: {Name}",
                        entity.Id, entity.staff_code, entity.staff_name);
                }
            }

            // 第三方删除成功后，再执行本地删除
            await _meetingStaffRep.FakeDeleteAsync(entity);   //假删除
            //await _meetingStaffRep.DeleteAsync(entity);   //真删除

            return true;
        });

        if (!result.IsSuccess)
        {
            throw Oops.Oh($"删除会议人员失败: {result.ErrorMessage}");
        }
    }

    /// <summary>
    /// 批量删除会议人员表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除会议人员表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteMeetingStaffInput> input)
    {
        var exp = Expressionable.Create<MeetingStaff>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _meetingStaffRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        // 使用事务确保批量删除的数据一致性
        var result = await _sqlSugarClient.Ado.UseTranAsync(async () =>
        {
            // 先同步删除第三方平台数据
            var failedStaffCodes = new List<string>();
            foreach (var entity in list.Where(x => !string.IsNullOrWhiteSpace(x.staff_code) && x.staff_id.HasValue))
            {
                var syncResult = await _greenDisplayService.DeleteMeetingStaffAsync(entity.staff_id.Value);
                if (!syncResult)
                {
                    failedStaffCodes.Add(entity.staff_code);
                    _logger.LogError("会议人员批量删除同步到第三方平台失败 - ID: {Id}, 员工编号: {StaffCode}, 姓名: {Name}",
                        entity.Id, entity.staff_code, entity.staff_name);
                }
                else
                {
                    _logger.LogInformation("会议人员批量删除同步到第三方平台成功 - ID: {Id}, 员工编号: {StaffCode}, 姓名: {Name}",
                        entity.Id, entity.staff_code, entity.staff_name);
                }
            }

            // 如果有第三方删除失败的情况，抛出异常回滚事务
            if (failedStaffCodes.Any())
            {
                var errorMessage = $"部分会议人员同步删除到第三方平台失败，员工编号: {string.Join(", ", failedStaffCodes)}";
                _logger.LogError(errorMessage);
                throw Oops.Oh(errorMessage);
            }

            // 所有第三方删除成功后，再执行本地批量删除
            var deleteResult = await _meetingStaffRep.FakeDeleteAsync(list);   //假删除
            //var deleteResult = await _meetingStaffRep.DeleteAsync(list);   //真删除

            return deleteResult;
        });

        if (!result.IsSuccess)
        {
            throw Oops.Oh($"批量删除会议人员失败: {result.ErrorMessage}");
        }

        return result.Data;
    }

    /// <summary>
    /// 导出会议人员表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出会议人员表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageMeetingStaffInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportMeetingStaffOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "会议人员表导出记录");
    }
    
    /// <summary>
    /// 下载会议人员表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载会议人员表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportMeetingStaffOutput>(), "会议人员表导入模板");
    }
    
    private static readonly object _meetingStaffImportLock = new object();
    /// <summary>
    /// 导入会议人员表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入会议人员表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (_meetingStaffImportLock)
        {
            var stream = ExcelHelper.ImportData<ImportMeetingStaffInput, MeetingStaff>(file, (list, markerErrorAction) =>
            {
                // 使用独立的数据库连接，避免与UnitOfWork冲突
                // 创建新的SqlSugarClient实例来避免连接重用问题
                using var db = _sqlSugarClient.CopyNew();
                
                db.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 使用事务确保数据一致性，包括第三方平台同步
                    var result = db.Ado.UseTran(() =>
                    {
                        // 校验并过滤必填基本类型为null的字段
                        var rows = pageItems.Where(x => {
                            if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                            return true;
                        }).Adapt<List<MeetingStaff>>();
                        
                        // 检查Excel文件内部的重复员工编号
                        var staffCodes = rows.Where(x => !string.IsNullOrWhiteSpace(x.staff_code) && x.IsDelete == false).Select(x => x.staff_code).ToList();
                        var duplicateStaffCodes = staffCodes.GroupBy(x => x).Where(g => g.Count() > 1).Select(g => g.Key).ToList();
                        
                        if (duplicateStaffCodes.Any())
                        {
                            // 标记Excel文件内部重复的员工编号为错误
                            foreach (var duplicateCode in duplicateStaffCodes)
                            {
                                var duplicateItems = pageItems.Where(p => p.staff_code == duplicateCode).ToList();
                                // 保留第一个，其余标记为错误
                                for (int i = 1; i < duplicateItems.Count; i++)
                                {
                                    duplicateItems[i].Error = $"员工编号 '{duplicateCode}' 在Excel文件中重复，请检查数据";
                                }
                            }
                            // 重新过滤有效数据
                            rows = pageItems.Where(x => string.IsNullOrWhiteSpace(x.Error)).Adapt<List<MeetingStaff>>();
                            staffCodes = rows.Where(x => !string.IsNullOrWhiteSpace(x.staff_code) && x.IsDelete == false).Select(x => x.staff_code).ToList();
                        }
                        
                        // 检查与数据库中已存在数据的重复（仅检查未删除的记录）
                        if (staffCodes.Count > 0)
                        {
                            var existingActiveStaffCodes = db.Queryable<MeetingStaff>()
                                .Where(x => staffCodes.Contains(x.staff_code) && x.IsDelete == false)
                                .Select(x => x.staff_code)
                                .ToList();
                            
                            if (existingActiveStaffCodes.Any())
                            {
                                // 标记与数据库中活跃记录重复的员工编号为错误
                                foreach (var row in rows.Where(x => existingActiveStaffCodes.Contains(x.staff_code)))
                                {
                                    var originalItem = pageItems.FirstOrDefault(p => p.staff_code == row.staff_code && string.IsNullOrWhiteSpace(p.Error));
                                    if (originalItem != null)
                                    {
                                        originalItem.Error = $"员工编号 '{row.staff_code}' 已存在于数据库中，请使用其他编号";
                                    }
                                }
                                // 过滤掉与活跃记录重复的记录
                                rows = rows.Where(x => !existingActiveStaffCodes.Contains(x.staff_code)).ToList();
                            }
                        }
                        
                        // 如果没有有效数据，跳过处理
                        if (!rows.Any())
                        {
                            return;
                        }
                        
                        // 检查第三方平台是否存在相同的员工编号
                        var conflictStaffCodes = new List<string>();
                        var thirdPartyCheckFailed = false;
                        
                        try
                        {
                            // 逐个检查每个员工编号是否在第三方平台存在
                            foreach (var staffCode in staffCodes)
                            {
                                try
                                {
                                    var queryInput = new QueryMeetingStaffInput
                                    {
                                        PageNo = 1,
                                        PageSize = 1,
                                        code = staffCode // 使用code字段精确查询特定工号
                                    };
                                    
                                    var thirdPartyResult = _greenDisplayService.QueryMeetingStaffAsync(queryInput).Result;
                                    
                                    // 如果查询到结果，说明该工号已存在于第三方平台
                                    if (thirdPartyResult?.list?.Any() == true)
                                    {
                                        conflictStaffCodes.Add(staffCode);
                                    }
                                }
                                catch (Exception queryEx)
                                {
                                    _logger.LogWarning(queryEx, "查询第三方平台员工编号 {StaffCode} 失败，跳过重复性检查", staffCode);
                                    thirdPartyCheckFailed = true;
                                    // 继续处理下一个员工编号，不中断整个流程
                                }
                            }
                            
                            if (conflictStaffCodes.Any())
                            {
                                // 标记与第三方平台重复的员工编号为错误
                                foreach (var conflictCode in conflictStaffCodes)
                                {
                                    var conflictItems = pageItems.Where(p => p.staff_code == conflictCode && string.IsNullOrWhiteSpace(p.Error)).ToList();
                                    foreach (var item in conflictItems)
                                    {
                                        item.Error = $"员工编号 '{conflictCode}' 已存在于第三方平台中，请使用其他编号";
                                    }
                                }
                                
                                _logger.LogWarning("导入数据中发现与第三方平台重复的员工编号: {ConflictCodes}", string.Join(", ", conflictStaffCodes));
                                throw new Exception($"发现与第三方平台重复的员工编号: {string.Join(", ", conflictStaffCodes)}，请修改后重新导入");
                            }
                            
                            // 如果第三方平台检查失败，记录警告但继续处理
                            if (thirdPartyCheckFailed)
                            {
                                _logger.LogWarning("第三方平台连接异常，无法完全验证员工编号重复性，将继续执行导入操作");
                            }
                        }
                        catch (Exception ex) when (!(ex.Message.Contains("发现与第三方平台重复的员工编号")))
                        {
                            _logger.LogWarning(ex, "检查第三方平台员工编号时发生错误，将跳过重复性检查继续处理");
                            // 不抛出异常，允许继续处理导入流程
                        }
                        
                        // 检查每个员工编号是否在数据库中存在（包括已删除的记录）
                        var allExistingStaffCodes = db.Queryable<MeetingStaff>()
                            .Where(x => staffCodes.Contains(x.staff_code))
                            .Select(x => new { x.staff_code, x.IsDelete, x.Id })
                            .ToList();
                        
                        var insertRows = new List<MeetingStaff>();
                        var updateRows = new List<MeetingStaff>();
                        var thirdPartyCreateList = new List<(MeetingStaff staff, CreateMeetingStaffInput input)>();
                        var thirdPartyUpdateList = new List<(MeetingStaff staff, Admin.NET.Plugin.GreenDisplay.Service.UpdateMeetingStaffInput input)>();
                        
                        foreach (var row in rows)
                        {
                            // 验证字段长度
                            if (row.staff_code?.Length > 50 || row.staff_name?.Length > 100 || 
                                row.position?.Length > 100 || row.department?.Length > 100 ||
                                row.email?.Length > 100 || row.phone?.Length > 20 ||
                                row.avatar_url?.Length > 500 || row.field1?.Length > 255 ||
                                row.field2?.Length > 255 || row.field3?.Length > 255 ||
                                row.field4?.Length > 255 || row.field5?.Length > 255 ||
                                row.field6?.Length > 255 || row.description?.Length > 255)
                            {
                                var originalItem = pageItems.FirstOrDefault(p => p.staff_code == row.staff_code && string.IsNullOrWhiteSpace(p.Error));
                                if (originalItem != null)
                                {
                                    originalItem.Error = "字段长度超出限制";
                                }
                                continue;
                            }
                            
                            var existingRecord = allExistingStaffCodes.FirstOrDefault(x => x.staff_code == row.staff_code);
                            if (existingRecord != null)
                            {
                                // 如果记录存在，更新它（无论是否已删除）
                                row.Id = existingRecord.Id;
                                row.IsDelete = false; // 恢复已删除的记录
                                updateRows.Add(row);
                                
                                // 准备第三方平台更新数据
                                var thirdPartyUpdateInput = new Admin.NET.Plugin.GreenDisplay.Service.UpdateMeetingStaffInput
                                {
                                    Code = row.staff_code,
                                    Name = row.staff_name,
                                    Mobile = row.phone,
                                    Company = row.department,
                                    Position = row.position,
                                    dept = row.department,
                                    Field1 = row.field1,
                                    Field2 = row.field2,
                                    Field3 = row.field3,
                                    Field4 = row.field4,
                                    Field5 = row.field5,
                                    Field6 = row.field6,
                                    Description = row.description
                                };
                                thirdPartyUpdateList.Add((row, thirdPartyUpdateInput));
                            }
                            else
                            {
                                // 如果记录不存在，插入新记录
                                insertRows.Add(row);
                                
                                // 准备第三方平台创建数据
                                var thirdPartyCreateInput = new CreateMeetingStaffInput
                                {
                                    Code = row.staff_code,
                                    Name = row.staff_name,
                                    Mobile = row.phone,
                                    Company = row.department,
                                    Position = row.position,
                                    dept = row.department,
                                    Field1 = row.field1,
                                    Field2 = row.field2,
                                    Field3 = row.field3,
                                    Field4 = row.field4,
                                    Field5 = row.field5,
                                    Field6 = row.field6,
                                    Description = row.description
                                };
                                thirdPartyCreateList.Add((row, thirdPartyCreateInput));
                            }
                        }
                        
                        // 先同步到第三方平台
                        try
                        {
                            // 处理新增记录的第三方平台同步
                            foreach (var (staff, createInput) in thirdPartyCreateList)
                            {
                                var thirdPartyId = _greenDisplayService.CreateMeetingStaffAsync(createInput).Result;
                                if (thirdPartyId <= 0)
                                {
                                    throw new Exception($"第三方平台创建员工失败: {staff.staff_code} - {staff.staff_name}");
                                }
                                staff.staff_id = thirdPartyId;
                                _logger.LogInformation("第三方平台创建员工成功: {StaffCode} - {StaffName}, ThirdPartyId: {ThirdPartyId}", 
                                    staff.staff_code, staff.staff_name, thirdPartyId);
                            }
                            
                            // 处理更新记录的第三方平台同步
                            foreach (var (staff, updateInput) in thirdPartyUpdateList)
                            {
                                if (staff.staff_id.HasValue)
                                {
                                    updateInput.Id = staff.staff_id.Value;
                                    var updateResult = _greenDisplayService.UpdateMeetingStaffAsync(updateInput).Result;
                                    if (!updateResult)
                                    {
                                        throw new Exception($"第三方平台更新员工失败: {staff.staff_code} - {staff.staff_name}");
                                    }
                                    _logger.LogInformation("第三方平台更新员工成功: {StaffCode} - {StaffName}", 
                                        staff.staff_code, staff.staff_name);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "第三方平台同步失败，回退所有事务");
                            throw new Exception($"第三方平台同步失败: {ex.Message}，所有操作已回退");
                        }
                        
                        // 第三方平台同步成功后，执行本地数据库操作
                        try
                        {
                            // 执行插入操作
                            if (insertRows.Any())
                            {
                                db.Insertable(insertRows).ExecuteCommand();
                                _logger.LogInformation("成功插入 {Count} 条员工记录", insertRows.Count);
                            }
                            
                            // 执行更新操作
                            if (updateRows.Any())
                            {
                                db.Updateable(updateRows)
                                    .UpdateColumns(it => new
                                    {
                                        it.staff_id,
                                        it.staff_code,
                                        it.staff_name,
                                        it.position,
                                        it.department,
                                        it.email,
                                        it.phone,
                                        it.avatar_url,
                                        it.field1,
                                        it.field2,
                                        it.field3,
                                        it.field4,
                                        it.field5,
                                        it.field6,
                                        it.description,
                                        it.IsDelete
                                    }).ExecuteCommand();
                                _logger.LogInformation("成功更新 {Count} 条员工记录", updateRows.Count);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "本地数据库操作失败，回退所有事务");
                            throw new Exception($"本地数据库操作失败: {ex.Message}，所有操作已回退");
                        }
                    });
                    
                    if (!result.IsSuccess)
                    {
                        _logger.LogError("导入数据事务失败: {ErrorMessage}", result.ErrorMessage);
                        throw new Exception($"导入数据失败: {result.ErrorMessage}");
                    }
                });
            });
            
            return stream;
        }
    }
}
