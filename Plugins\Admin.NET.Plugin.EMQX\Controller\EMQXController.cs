// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// EMQX MQTT服务控制器
/// </summary>
[ApiDescriptionSettings("EMQX", Name = "MQTT服务", Order = 100)]
public class EMQXController : IDynamicApiController, ITransient
{
    private readonly EMQXService _emqxService;
    private readonly ILogger<EMQXController> _logger;

    public EMQXController(EMQXService emqxService, ILogger<EMQXController> logger)
    {
        _emqxService = emqxService;
        _logger = logger;
    }

    /// <summary>
    /// 获取连接状态
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>连接状态</returns>
    [HttpGet("/api/emqx/connection/status")]
    [DisplayName("获取MQTT连接状态")]
    public async Task<MqttConnectionDto> GetConnectionStatusAsync(string clientId)
    {
        try
        {
            _logger.LogInformation("获取MQTT连接状态，ClientId: {ClientId}", clientId);
            var status = await _emqxService.GetConnectionStatusAsync(clientId);
            _logger.LogInformation("MQTT连接状态: {Status}", status.Status);
            return status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT连接状态失败");
            throw Oops.Oh("获取MQTT连接状态失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 断开MQTT连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>断开结果</returns>
    [HttpPost("/api/emqx/disconnect")]
    [DisplayName("断开MQTT连接")]
    public async Task<bool> DisconnectAsync(string clientId)
    {
        try
        {
            _logger.LogInformation("开始断开MQTT连接，ClientId: {ClientId}", clientId);
            var result = await _emqxService.DisconnectAsync(clientId);
            _logger.LogInformation("MQTT断开结果: {Result}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "断开MQTT连接失败，ClientId: {ClientId}", clientId);
            throw Oops.Oh("断开MQTT连接失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 发布消息
    /// </summary>
    /// <param name="input">发布参数</param>
    /// <returns>发布结果</returns>
    [HttpPost("/api/emqx/publish")]
    [DisplayName("发布MQTT消息")]
    public async Task<bool> PublishAsync(MqttPublishInput input)
    {
        try
        {
            _logger.LogInformation("开始发布MQTT消息，Topic: {Topic}, ClientId: {ClientId}", input.Topic, input.ClientId);
            var result = await _emqxService.PublishAsync(input);
            _logger.LogInformation("MQTT消息发布结果: {Result}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发布MQTT消息失败，Topic: {Topic}, ClientId: {ClientId}", input.Topic, input.ClientId);
            throw Oops.Oh("发布MQTT消息失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 订阅主题
    /// </summary>
    /// <param name="input">订阅参数</param>
    /// <returns>订阅结果</returns>
    [HttpPost("/api/emqx/subscribe")]
    [DisplayName("订阅MQTT主题")]
    public async Task<bool> SubscribeAsync(MqttSubscribeInput input)
    {
        try
        {
            _logger.LogInformation("开始订阅MQTT主题，Topic: {Topic}, ClientId: {ClientId}", input.Topic, input.ClientId);
            var result = await _emqxService.SubscribeAsync(input);
            _logger.LogInformation("MQTT主题订阅结果: {Result}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "订阅MQTT主题失败，Topic: {Topic}, ClientId: {ClientId}", input.Topic, input.ClientId);
            throw Oops.Oh("订阅MQTT主题失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 取消订阅主题
    /// </summary>
    /// <param name="input">取消订阅参数</param>
    /// <returns>取消订阅结果</returns>
    [HttpPost("/api/emqx/unsubscribe")]
    [DisplayName("取消订阅MQTT主题")]
    public async Task<bool> UnsubscribeAsync(MqttUnsubscribeInput input)
    {
        try
        {
            _logger.LogInformation("开始取消订阅MQTT主题，Topic: {Topic}, ClientId: {ClientId}", input.Topic, input.ClientId);
            var result = await _emqxService.UnsubscribeAsync(input);
            _logger.LogInformation("MQTT主题取消订阅结果: {Result}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "取消订阅MQTT主题失败，Topic: {Topic}, ClientId: {ClientId}", input.Topic, input.ClientId);
            throw Oops.Oh("取消订阅MQTT主题失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取连接列表
    /// </summary>
    /// <returns>连接列表</returns>
    [HttpGet("/api/emqx/connections")]
    [DisplayName("获取MQTT连接列表")]
    public async Task<List<MqttConnectionDto>> GetConnectionsAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT连接列表");
            var connections = await _emqxService.GetConnectionsAsync();
            _logger.LogInformation("获取到 {Count} 个MQTT连接", connections.Count);
            return connections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT连接列表失败");
            throw Oops.Oh("获取MQTT连接列表失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取订阅列表
    /// </summary>
    /// <param name="clientId">客户端ID（可选）</param>
    /// <returns>订阅列表</returns>
    [HttpGet("/api/emqx/subscriptions")]
    [DisplayName("获取MQTT订阅列表")]
    public async Task<List<MqttSubscriptionDto>> GetSubscriptionsAsync(string clientId = null)
    {
        try
        {
            _logger.LogInformation("获取MQTT订阅列表，ClientId: {ClientId}", clientId);
            var subscriptions = await _emqxService.GetSubscriptionsAsync(clientId);
            _logger.LogInformation("获取到 {Count} 个MQTT订阅", subscriptions.Count);
            return subscriptions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT订阅列表失败，ClientId: {ClientId}", clientId);
            throw Oops.Oh("获取MQTT订阅列表失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    [HttpGet("/api/emqx/statistics")]
    [DisplayName("获取MQTT统计信息")]
    public async Task<MqttStatisticsDto> GetStatisticsAsync()
    {
        try
        {
            _logger.LogInformation("获取MQTT统计信息");
            var statistics = await _emqxService.GetStatisticsAsync();
            _logger.LogInformation("获取MQTT统计信息成功");
            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT统计信息失败");
            throw Oops.Oh("获取MQTT统计信息失败: " + ex.Message);
        }
    }


    /// <summary>
    /// 强制断开客户端连接
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("/api/emqx/force-disconnect")]
    [DisplayName("强制断开MQTT客户端")]
    public async Task<bool> ForceDisconnectAsync(string clientId)
    {
        try
        {
            _logger.LogInformation("强制断开MQTT客户端，ClientId: {ClientId}", clientId);
            var result = await _emqxService.DisconnectAsync(clientId);
            _logger.LogInformation("强制断开MQTT客户端结果: {Result}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "强制断开MQTT客户端失败，ClientId: {ClientId}", clientId);
            throw Oops.Oh("强制断开MQTT客户端失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 批量发布消息
    /// </summary>
    /// <param name="messages">消息列表</param>
    /// <returns>发布结果</returns>
    [HttpPost("/api/emqx/batch-publish")]
    [DisplayName("批量发布MQTT消息")]
    public async Task<List<BatchPublishResultDto>> BatchPublishAsync(List<MqttPublishInput> messages)
    {
        var results = new List<BatchPublishResultDto>();
        
        try
        {
            _logger.LogInformation("开始批量发布MQTT消息，消息数量: {Count}", messages.Count);
            
            foreach (var message in messages)
            {
                try
                {
                    var success = await _emqxService.PublishAsync(message);
                    results.Add(new BatchPublishResultDto
                    {
                        Topic = message.Topic,
                        ClientId = message.ClientId,
                        Success = success,
                        ErrorMessage = success ? null : "发布失败"
                    });
                }
                catch (Exception ex)
                {
                    results.Add(new BatchPublishResultDto
                    {
                        Topic = message.Topic,
                        ClientId = message.ClientId,
                        Success = false,
                        ErrorMessage = ex.Message
                    });
                }
            }
            
            _logger.LogInformation("批量发布MQTT消息完成，成功: {Success}, 失败: {Failed}", 
                results.Count(r => r.Success), results.Count(r => !r.Success));
            
            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "批量发布MQTT消息失败");
            throw Oops.Oh("批量发布MQTT消息失败: " + ex.Message);
        }
    }

    /// <summary>
    /// 获取主题权限
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topic">主题</param>
    /// <returns>权限信息</returns>
    [HttpGet("/api/emqx/topic-permission")]
    [DisplayName("获取MQTT主题权限")]
    public async Task<TopicPermissionDto> GetTopicPermissionAsync(string clientId, string topic)
    {
        try
        {
            _logger.LogInformation("获取MQTT主题权限，ClientId: {ClientId}, Topic: {Topic}", clientId, topic);
            
            // 模拟权限检查逻辑
            var permission = new TopicPermissionDto
            {
                ClientId = clientId,
                Topic = topic,
                CanPublish = true,
                CanSubscribe = true,
                CanRead = true,
                CanWrite = true,
                Timestamp = DateTime.Now
            };
            
            _logger.LogInformation("获取MQTT主题权限成功");
            return permission;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取MQTT主题权限失败，ClientId: {ClientId}, Topic: {Topic}", clientId, topic);
            throw Oops.Oh("获取MQTT主题权限失败: " + ex.Message);
        }
    }
}

