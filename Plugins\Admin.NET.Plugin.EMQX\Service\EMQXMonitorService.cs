// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using System.Collections.Concurrent;

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// EMQX监控服务
/// </summary>
[ApiDescriptionSettings("EMQX", Name = "MQTT监控", Order = 102)]
public class EMQXMonitorService : IDynamicApiController, ITransient
{
    private readonly EMQXService _emqxService;
    private readonly ConcurrentDictionary<DateTime, MqttTrafficDto> _trafficHistory;
    private readonly ConcurrentDictionary<string, MqttTopicStatisticsDto> _topicStatistics;
    private readonly object _lockObject = new();

    public EMQXMonitorService(EMQXService emqxService)
    {
        _emqxService = emqxService;
        _trafficHistory = new ConcurrentDictionary<DateTime, MqttTrafficDto>();
        _topicStatistics = new ConcurrentDictionary<string, MqttTopicStatisticsDto>();
        
        // 初始化模拟数据
        InitializeMockData();
    }

    /// <summary>
    /// 获取实时监控数据
    /// </summary>
    /// <returns>监控数据</returns>
    [HttpGet("/api/emqx/monitor/realtime")]
    public async Task<RealtimeMonitorDto> GetRealtimeDataAsync()
    {
        var statistics = await _emqxService.GetStatisticsAsync();
        var connections = await _emqxService.GetConnectionsAsync();
        var subscriptions = await _emqxService.GetSubscriptionsAsync();
        
        var realtimeData = new RealtimeMonitorDto
        {
            Timestamp = DateTime.Now,
            OnlineClients = statistics.OnlineConnections,
            TotalClients = statistics.TotalConnections,
            ActiveSubscriptions = statistics.ActiveSubscriptions,
            TotalSubscriptions = statistics.TotalSubscriptions,
            MessagesSentPerSecond = CalculateMessageRate(true),
            MessagesReceivedPerSecond = CalculateMessageRate(false),
            BytesSentPerSecond = CalculateByteRate(true),
            BytesReceivedPerSecond = CalculateByteRate(false),
            CpuUsage = statistics.CpuUsage,
            MemoryUsage = statistics.MemoryUsage,
            ConnectionSuccessRate = statistics.ConnectionSuccessRate,
            ServerStatus = statistics.ServerStatus,
            Uptime = statistics.ServerUptime
        };
        
        return realtimeData;
    }

    /// <summary>
    /// 获取流量历史数据
    /// </summary>
    /// <param name="hours">小时数</param>
    /// <returns>流量历史</returns>
    [HttpGet("/api/emqx/monitor/traffic")]
    public Task<List<MqttTrafficDto>> GetTrafficHistoryAsync(int hours = 24)
    {
        var startTime = DateTime.Now.AddHours(-hours);
        var trafficData = _trafficHistory.Values
            .Where(t => t.Timestamp >= startTime)
            .OrderBy(t => t.Timestamp)
            .ToList();
            
        return Task.FromResult(trafficData);
    }

    /// <summary>
    /// 获取主题统计信息
    /// </summary>
    /// <returns>主题统计</returns>
    [HttpGet("/api/emqx/monitor/topics")]
    public Task<List<MqttTopicStatisticsDto>> GetTopicStatisticsAsync()
    {
        var topicStats = _topicStatistics.Values
            .OrderByDescending(t => t.MessageCount)
            .ToList();
            
        return Task.FromResult(topicStats);
    }

    /// <summary>
    /// 获取连接状态分布
    /// </summary>
    /// <returns>连接状态分布</returns>
    [HttpGet("/api/emqx/monitor/connection-status")]
    public async Task<List<ConnectionStatusDto>> GetConnectionStatusDistributionAsync()
    {
        var connections = await _emqxService.GetConnectionsAsync();
        
        var statusDistribution = connections
            .GroupBy(c => c.Status)
            .Select(g => new ConnectionStatusDto
            {
                Status = g.Key,
                Count = g.Count(),
                Percentage = connections.Count > 0 ? (decimal)g.Count() / connections.Count * 100 : 0
            })
            .ToList();
            
        return statusDistribution;
    }

    /// <summary>
    /// 获取消息类型分布
    /// </summary>
    /// <returns>消息类型分布</returns>
    [HttpGet("/api/emqx/monitor/message-types")]
    public Task<List<MessageTypeDto>> GetMessageTypeDistributionAsync()
    {
        // 模拟数据，实际应该从消息历史中统计
        var messageTypes = new List<MessageTypeDto>
        {
            new() { MessageType = MqttMessageTypeEnum.DeviceData, Count = 1250, Percentage = 45.5m },
            new() { MessageType = MqttMessageTypeEnum.DeviceStatus, Count = 890, Percentage = 32.4m },
            new() { MessageType = MqttMessageTypeEnum.DeviceControl, Count = 320, Percentage = 11.6m },
            new() { MessageType = MqttMessageTypeEnum.SystemMessage, Count = 180, Percentage = 6.5m },
            new() { MessageType = MqttMessageTypeEnum.CustomMessage, Count = 110, Percentage = 4.0m }
        };
        
        return Task.FromResult(messageTypes);
    }

    /// <summary>
    /// 获取性能指标
    /// </summary>
    /// <returns>性能指标</returns>
    [HttpGet("/api/emqx/monitor/performance")]
    public async Task<PerformanceMetricsDto> GetPerformanceMetricsAsync()
    {
        var statistics = await _emqxService.GetStatisticsAsync();
        
        var metrics = new PerformanceMetricsDto
        {
            Timestamp = DateTime.Now,
            CpuUsage = statistics.CpuUsage,
            MemoryUsage = statistics.MemoryUsage,
            DiskUsage = 65.2m, // 模拟数据
            NetworkInbound = CalculateByteRate(false),
            NetworkOutbound = CalculateByteRate(true),
            ConnectionsPerSecond = CalculateConnectionRate(),
            MessagesPerSecond = CalculateMessageRate(true) + CalculateMessageRate(false),
            AverageLatency = 12.5m, // 模拟数据
            ErrorRate = 0.02m // 模拟数据
        };
        
        return metrics;
    }

    /// <summary>
    /// 获取告警信息
    /// </summary>
    /// <returns>告警列表</returns>
    [HttpGet("/api/emqx/monitor/alerts")]
    public Task<List<AlertDto>> GetAlertsAsync()
    {
        var alerts = new List<AlertDto>();
        
        // 检查CPU使用率
        if (85.0m > 80) // 模拟高CPU使用率
        {
            alerts.Add(new AlertDto
            {
                Id = Guid.NewGuid().ToString(),
                Level = AlertLevel.Warning,
                Title = "CPU使用率过高",
                Message = "CPU使用率超过80%，当前为85%",
                Timestamp = DateTime.Now.AddMinutes(-5),
                IsResolved = false
            });
        }
        
        // 检查内存使用率
        if (92.0m > 90)
        {
            alerts.Add(new AlertDto
            {
                Id = Guid.NewGuid().ToString(),
                Level = AlertLevel.Critical,
                Title = "内存使用率过高",
                Message = "内存使用率超过90%，当前为92%",
                Timestamp = DateTime.Now.AddMinutes(-2),
                IsResolved = false
            });
        }
        
        return Task.FromResult(alerts);
    }

    /// <summary>
    /// 导出监控报告
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>报告数据</returns>
    [HttpPost("/api/emqx/monitor/export")]
    public async Task<MonitorReportDto> ExportReportAsync(DateTime startTime, DateTime endTime)
    {
        var statistics = await _emqxService.GetStatisticsAsync();
        var connections = await _emqxService.GetConnectionsAsync();
        var trafficData = await GetTrafficHistoryAsync((int)(endTime - startTime).TotalHours);
        
        var report = new MonitorReportDto
        {
            ReportId = Guid.NewGuid().ToString(),
            StartTime = startTime,
            EndTime = endTime,
            GenerateTime = DateTime.Now,
            Summary = new ReportSummaryDto
            {
                TotalConnections = statistics.TotalConnections,
                PeakOnlineConnections = trafficData.Max(t => t.OnlineClients),
                TotalMessages = statistics.TotalSentMessages + statistics.TotalReceivedMessages,
                AverageLatency = 12.5m,
                UptimePercentage = 99.95m
            },
            TrafficData = trafficData,
            TopicStatistics = await GetTopicStatisticsAsync()
        };
        
        return report;
    }

    /// <summary>
    /// 初始化模拟数据
    /// </summary>
    private void InitializeMockData()
    {
        // 生成过去24小时的流量数据
        var now = DateTime.Now;
        var random = new Random();
        
        for (int i = 24; i >= 0; i--)
        {
            var timestamp = now.AddHours(-i);
            var traffic = new MqttTrafficDto
            {
                Timestamp = timestamp,
                SentMessages = random.Next(100, 500),
                ReceivedMessages = random.Next(80, 400),
                SentBytes = random.Next(10000, 50000),
                ReceivedBytes = random.Next(8000, 40000),
                OnlineClients = random.Next(10, 50)
            };
            _trafficHistory.TryAdd(timestamp, traffic);
        }
        
        // 生成主题统计数据
        var topics = new[]
        {
            EMQXConst.DEVICE_STATUS_TOPIC,
            EMQXConst.DEVICE_DATA_TOPIC,
            EMQXConst.DEVICE_CONTROL_TOPIC,
            "system/message",
            "custom/device001",
            "custom/device002"
        };
        
        foreach (var topic in topics)
        {
            var stats = new MqttTopicStatisticsDto
            {
                Topic = topic,
                SubscriberCount = random.Next(1, 20),
                MessageCount = random.Next(100, 2000),
                LastMessageTime = now.AddMinutes(-random.Next(1, 60)),
                AverageMessageSize = random.Next(100, 1000),
                MessageType = GetMessageTypeByTopic(topic)
            };
            _topicStatistics.TryAdd(topic, stats);
        }
    }

    /// <summary>
    /// 根据主题获取消息类型
    /// </summary>
    private MqttMessageTypeEnum GetMessageTypeByTopic(string topic)
    {
        return topic switch
        {
            EMQXConst.DEVICE_STATUS_TOPIC => MqttMessageTypeEnum.DeviceStatus,
            EMQXConst.DEVICE_DATA_TOPIC => MqttMessageTypeEnum.DeviceData,
            EMQXConst.DEVICE_CONTROL_TOPIC => MqttMessageTypeEnum.DeviceControl,
            "system/message" => MqttMessageTypeEnum.SystemMessage,
            _ => MqttMessageTypeEnum.CustomMessage
        };
    }

    /// <summary>
    /// 计算消息速率
    /// </summary>
    private decimal CalculateMessageRate(bool sent)
    {
        // 模拟计算，实际应该基于真实数据
        var random = new Random();
        return sent ? random.Next(50, 150) : random.Next(40, 120);
    }

    /// <summary>
    /// 计算字节速率
    /// </summary>
    private decimal CalculateByteRate(bool sent)
    {
        // 模拟计算，实际应该基于真实数据
        var random = new Random();
        return sent ? random.Next(5000, 15000) : random.Next(4000, 12000);
    }

    /// <summary>
    /// 计算连接速率
    /// </summary>
    private decimal CalculateConnectionRate()
    {
        // 模拟计算，实际应该基于真实数据
        var random = new Random();
        return random.Next(1, 10);
    }
}

/// <summary>
/// 实时监控数据DTO
/// </summary>
public class RealtimeMonitorDto
{
    public DateTime Timestamp { get; set; }
    public int OnlineClients { get; set; }
    public int TotalClients { get; set; }
    public int ActiveSubscriptions { get; set; }
    public int TotalSubscriptions { get; set; }
    public decimal MessagesSentPerSecond { get; set; }
    public decimal MessagesReceivedPerSecond { get; set; }
    public decimal BytesSentPerSecond { get; set; }
    public decimal BytesReceivedPerSecond { get; set; }
    public decimal CpuUsage { get; set; }
    public decimal MemoryUsage { get; set; }
    public decimal ConnectionSuccessRate { get; set; }
    public string ServerStatus { get; set; }
    public long Uptime { get; set; }
}

/// <summary>
/// 连接状态分布DTO
/// </summary>
public class ConnectionStatusDto
{
    public MqttConnectionStatusEnum Status { get; set; }
    public int Count { get; set; }
    public decimal Percentage { get; set; }
}

/// <summary>
/// 消息类型分布DTO
/// </summary>
public class MessageTypeDto
{
    public MqttMessageTypeEnum MessageType { get; set; }
    public long Count { get; set; }
    public decimal Percentage { get; set; }
}

/// <summary>
/// 性能指标DTO
/// </summary>
public class PerformanceMetricsDto
{
    public DateTime Timestamp { get; set; }
    public decimal CpuUsage { get; set; }
    public decimal MemoryUsage { get; set; }
    public decimal DiskUsage { get; set; }
    public decimal NetworkInbound { get; set; }
    public decimal NetworkOutbound { get; set; }
    public decimal ConnectionsPerSecond { get; set; }
    public decimal MessagesPerSecond { get; set; }
    public decimal AverageLatency { get; set; }
    public decimal ErrorRate { get; set; }
}

/// <summary>
/// 告警级别枚举
/// </summary>
public enum AlertLevel
{
    Info = 1,
    Warning = 2,
    Critical = 3
}

/// <summary>
/// 告警DTO
/// </summary>
public class AlertDto
{
    public string Id { get; set; }
    public AlertLevel Level { get; set; }
    public string Title { get; set; }
    public string Message { get; set; }
    public DateTime Timestamp { get; set; }
    public bool IsResolved { get; set; }
}

/// <summary>
/// 监控报告DTO
/// </summary>
public class MonitorReportDto
{
    public string ReportId { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public DateTime GenerateTime { get; set; }
    public ReportSummaryDto Summary { get; set; }
    public List<MqttTrafficDto> TrafficData { get; set; }
    public List<MqttTopicStatisticsDto> TopicStatistics { get; set; }
}

/// <summary>
/// 报告摘要DTO
/// </summary>
public class ReportSummaryDto
{
    public int TotalConnections { get; set; }
    public int PeakOnlineConnections { get; set; }
    public long TotalMessages { get; set; }
    public decimal AverageLatency { get; set; }
    public decimal UptimePercentage { get; set; }
}