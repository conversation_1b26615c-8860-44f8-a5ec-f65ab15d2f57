fail: 2025-07-24 10:54:52.0571353 +08:00 Thursday L Admin.NET.Application.AccessPointsService[0] #23 '00-758c32aed965b3918d8a32da795bb21d-bf1ce3abb1a2da5d-00'
      [Admin.NET.Application.dll] async Task Admin.NET.Application.AccessPointsService.Delete(DeleteAccessPointsInput input)+(?) => { }
      第三方平台删除AP失败，MAC地址: D4:AD:20:B8:97:99
crit: 2025-07-24 11:07:56.7256536 +08:00 Thursday L System.Logging.TaskQueueService[0] #53
      [Furion.Pure.dll] async Task Furion.TaskQueue.TaskQueueHostedService.ExecuteAsync(CancellationToken stoppingToken)
      TaskQueue hosted service is stopped.
fail: 2025-07-24 11:18:40.1891713 +08:00 Thursday L System.Logging.StringLogging[0] #29
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:18:40——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Can't replace active reader.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:18:40.1548653 +08:00 Thursday L System.Logging.StringLogging[0] #31
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:18:40——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Failed to read the result set.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:18:40.7744819 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #29
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Can't replace active reader.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:18:40.8185629 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #31
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.ArgumentOutOfRangeException: value must be between 0 and 1 (Parameter 'value')
         at MySqlConnector.Protocol.Serialization.ByteArrayReader.set_Offset(Int32 value) in /_/src/MySqlConnector/Protocol/Serialization/ByteArrayReader.cs:line 17
         at MySqlConnector.Protocol.Payloads.ColumnDefinitionPayload.SkipLengthEncodedByteString(ByteArrayReader& reader) in /_/src/MySqlConnector/Protocol/Payloads/ColumnDefinitionPayload.cs:line 103
         at MySqlConnector.Protocol.Payloads.ColumnDefinitionPayload.Create(ResizableArraySegment`1 arraySegment) in /_/src/MySqlConnector/Protocol/Payloads/ColumnDefinitionPayload.cs:line 82
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 145
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:19:19.0415381 +08:00 Thursday L System.Logging.StringLogging[0] #51
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-24 11:19:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:19:19.7155758 +08:00 Thursday L System.Logging.StringLogging[0] #29
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-24 11:19:19——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:19:21.3982592 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:19:21.3996857 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #51
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:11.1045681 +08:00 Thursday L System.Logging.StringLogging[0] #16
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:11——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Can't replace active reader.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:11.1045843 +08:00 Thursday L System.Logging.StringLogging[0] #34
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:11——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Can't replace active reader.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:11.1045814 +08:00 Thursday L System.Logging.StringLogging[0] #36
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:11——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Failed to read the result set.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:13.1778899 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #16
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Can't replace active reader.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:14.0656347 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #34
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Can't replace active reader.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:14.1825780 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #36
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.ArgumentOutOfRangeException: value must be between 0 and 1 (Parameter 'value')
         at MySqlConnector.Protocol.Serialization.ByteArrayReader.set_Offset(Int32 value) in /_/src/MySqlConnector/Protocol/Serialization/ByteArrayReader.cs:line 17
         at MySqlConnector.Protocol.Payloads.ColumnDefinitionPayload.Create(ResizableArraySegment`1 arraySegment) in /_/src/MySqlConnector/Protocol/Payloads/ColumnDefinitionPayload.cs:line 82
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 145
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:14.2608449 +08:00 Thursday L System.Logging.StringLogging[0] #32
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:14——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:13.2427325 +08:00 Thursday L System.Logging.StringLogging[0] #33
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:13——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:19.2573847 +08:00 Thursday L System.Logging.StringLogging[0] #36
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:19——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:20.2298074 +08:00 Thursday L System.Logging.StringLogging[0] #34
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:19——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:20.7387796 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #32
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:21.4029713 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #33
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:21.6058785 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #36
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:22.3461093 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #34
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:21.8792678 +08:00 Thursday L System.Logging.StringLogging[0] #19
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:21——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:23.6275777 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #19
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:43.9000884 +08:00 Thursday L System.Logging.StringLogging[0] #37
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 11:49:43——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:44.3113152 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #37
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:52:07.7753192 +08:00 Thursday L System.Logging.StringLogging[0] #4
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 11:52:07——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:52:08.7627844 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #4
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:52:11.1649258 +08:00 Thursday L System.Logging.StringLogging[0] #34
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 11:52:11——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:52:11.3443103 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #34
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:54:43.3514333 +08:00 Thursday L System.Logging.StringLogging[0] #7
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 11:54:43——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:54:43.5700553 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #7
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:02:53.1675428 +08:00 Thursday L System.Logging.StringLogging[0] #19
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:02:52——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:02:54.0111810 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #19
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:04:19.0101327 +08:00 Thursday L System.Logging.StringLogging[0] #34
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:04:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:04:19.7078333 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #34
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:17:54.0101786 +08:00 Thursday L System.Logging.StringLogging[0] #31
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:17:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:17:55.7232829 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #31
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:17:55.8435551 +08:00 Thursday L System.Logging.StringLogging[0] #7
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:17:55——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:17:56.1494125 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #7
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:19:18.5450877 +08:00 Thursday L System.Logging.StringLogging[0] #32
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:19:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:19:20.0582097 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #32
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:32:53.7703066 +08:00 Thursday L System.Logging.StringLogging[0] #14
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:32:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:32:54.2128976 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #14
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:34:18.1622453 +08:00 Thursday L System.Logging.StringLogging[0] #56
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:34:17——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:34:19.4295709 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #56
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:47:53.6186400 +08:00 Thursday L System.Logging.StringLogging[0] #30
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:47:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:47:54.3389069 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #30
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:49:18.2289925 +08:00 Thursday L System.Logging.StringLogging[0] #57
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:49:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:49:19.3301692 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #57
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:02:53.3725711 +08:00 Thursday L System.Logging.StringLogging[0] #58
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:02:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:02:54.5003175 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #58
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:04:20.0093046 +08:00 Thursday L System.Logging.StringLogging[0] #55
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:04:19——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:04:20.8672048 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #55
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:17:53.6308922 +08:00 Thursday L System.Logging.StringLogging[0] #29
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:17:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:17:54.5285101 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #29
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:19:18.6368608 +08:00 Thursday L System.Logging.StringLogging[0] #31
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:19:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:19:18.9623239 +08:00 Thursday L System.Logging.StringLogging[0] #52
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:19:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:19:19.9608394 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #31
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:19:20.2342239 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #52
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:32:53.2822636 +08:00 Thursday L System.Logging.StringLogging[0] #29
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:32:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:32:54.1119231 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #29
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:34:18.9428038 +08:00 Thursday L System.Logging.StringLogging[0] #59
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:34:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:34:20.0107591 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #59
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:49:18.9240670 +08:00 Thursday L System.Logging.StringLogging[0] #31
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:49:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:49:20.0819401 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #31
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:02:53.3914565 +08:00 Thursday L System.Logging.StringLogging[0] #55
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:02:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:02:53.4911666 +08:00 Thursday L System.Logging.StringLogging[0] #31
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:02:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:02:54.2088821 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #55
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:02:54.6477359 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #31
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:04:18.9559471 +08:00 Thursday L System.Logging.StringLogging[0] #57
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:04:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:04:20.2589266 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #57
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:20.2285339 +08:00 Thursday L System.Logging.StringLogging[0] #61
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:19:20——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:20.3555734 +08:00 Thursday L System.Logging.StringLogging[0] #30
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:19:20——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:20.5079183 +08:00 Thursday L System.Logging.StringLogging[0] #29
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:19:20——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:21.1241729 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #61
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:21.5844717 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #30
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:21.6568292 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #29
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 16:15:39.9580163 +08:00 Thursday L Admin.NET.Application.AccessPointsService[0] #24 '00-0e5001ea5ed4e940e4baa96818853a60-c722d59bad38ddd8-00'
      [Admin.NET.Application.dll] async Task<bool> Admin.NET.Application.AccessPointsService.command(UpdateAPCommandInput updateAPCommandInput)
      AP网关命令下发时发生异常，MAC地址: (null), 命令: 0000      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Furion.FriendlyException.AppFriendlyException: MAC地址不能为空
         at Admin.NET.Application.AccessPointsService.command(UpdateAPCommandInput updateAPCommandInput) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\AccessPointsService.cs:line 470
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 16:16:50.8906221 +08:00 Thursday L Admin.NET.Application.AccessPointsService[0] #23 '00-24bad4adf618d82475455f6aa39faa8c-a31b950596390b85-00'
      [Admin.NET.Application.dll] async Task<bool> Admin.NET.Application.AccessPointsService.command(UpdateAPCommandInput updateAPCommandInput)
      AP网关命令下发时发生异常，MAC地址: (null), 命令: 0000      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Furion.FriendlyException.AppFriendlyException: MAC地址不能为空
         at Admin.NET.Application.AccessPointsService.command(UpdateAPCommandInput updateAPCommandInput) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\AccessPointsService.cs:line 470
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
