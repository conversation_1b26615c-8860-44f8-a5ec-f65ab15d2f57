// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// EMQX配置选项
/// </summary>
public sealed class EMQXOptions : IConfigurableOptions
{
    /// <summary>
    /// MQTT服务器地址
    /// </summary>
    public string Server { get; set; } = "localhost";

    /// <summary>
    /// MQTT服务器端口
    /// </summary>
    public int Port { get; set; } = 1883;

    /// <summary>
    /// 是否使用TLS
    /// </summary>
    public bool UseTLS { get; set; } = false;

    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 客户端ID前缀
    /// </summary>
    public string ClientIdPrefix { get; set; } = "AdminNET";

    /// <summary>
    /// 保持连接时间(秒)
    /// </summary>
    public int KeepAlivePeriod { get; set; } = 60;

    /// <summary>
    /// 清理会话
    /// </summary>
    public bool CleanSession { get; set; } = true;

    /// <summary>
    /// 连接超时时间(秒)
    /// </summary>
    public int ConnectionTimeout { get; set; } = 30;

    /// <summary>
    /// 重试间隔(秒)
    /// </summary>
    public int RetryInterval { get; set; } = 5;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// 阿里云MQTT配置
    /// </summary>
    public AliyunMqttOptions AliyunMqtt { get; set; } = new();
}

/// <summary>
/// 阿里云MQTT配置选项
/// </summary>
public sealed class AliyunMqttOptions
{
    /// <summary>
    /// 是否启用阿里云MQTT
    /// </summary>
    public bool Enabled { get; set; } = false;

    /// <summary>
    /// 实例ID
    /// </summary>
    public string InstanceId { get; set; } = string.Empty;

    /// <summary>
    /// AccessKey ID
    /// </summary>
    public string AccessKeyId { get; set; } = string.Empty;

    /// <summary>
    /// AccessKey Secret
    /// </summary>
    public string AccessKeySecret { get; set; } = string.Empty;

    /// <summary>
    /// 组ID
    /// </summary>
    public string GroupId { get; set; } = "GID_DEFAULT";

    /// <summary>
    /// 地域
    /// </summary>
    public string Region { get; set; } = "cn-hangzhou";
}