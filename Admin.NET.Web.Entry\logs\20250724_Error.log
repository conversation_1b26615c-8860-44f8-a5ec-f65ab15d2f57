fail: 2025-07-24 10:54:52.3430480 +08:00 Thursday L Admin.NET.Application.AccessPointsService[0] #23 '00-758c32aed965b3918d8a32da795bb21d-bf1ce3abb1a2da5d-00'
      [Admin.NET.Application.dll] async Task Admin.NET.Application.AccessPointsService.Delete(DeleteAccessPointsInput input)+(?) => { }
      第三方平台删除AP失败，MAC地址: D4:AD:20:B8:97:99
crit: 2025-07-24 11:07:56.7277185 +08:00 Thursday L System.Logging.TaskQueueService[0] #53
      [Furion.Pure.dll] async Task Furion.TaskQueue.TaskQueueHostedService.ExecuteAsync(CancellationToken stoppingToken)
      TaskQueue hosted service is stopped.
fail: 2025-07-24 11:18:40.3802437 +08:00 Thursday L System.Logging.StringLogging[0] #29
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:18:40——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Can't replace active reader.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:18:40.3850114 +08:00 Thursday L System.Logging.StringLogging[0] #31
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:18:40——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Failed to read the result set.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:18:41.2472657 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #29
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Can't replace active reader.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:18:41.2667407 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #31
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.ArgumentOutOfRangeException: value must be between 0 and 1 (Parameter 'value')
         at MySqlConnector.Protocol.Serialization.ByteArrayReader.set_Offset(Int32 value) in /_/src/MySqlConnector/Protocol/Serialization/ByteArrayReader.cs:line 17
         at MySqlConnector.Protocol.Payloads.ColumnDefinitionPayload.SkipLengthEncodedByteString(ByteArrayReader& reader) in /_/src/MySqlConnector/Protocol/Payloads/ColumnDefinitionPayload.cs:line 103
         at MySqlConnector.Protocol.Payloads.ColumnDefinitionPayload.Create(ResizableArraySegment`1 arraySegment) in /_/src/MySqlConnector/Protocol/Payloads/ColumnDefinitionPayload.cs:line 82
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 145
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:19:20.4703123 +08:00 Thursday L System.Logging.StringLogging[0] #29
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-24 11:19:19——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:19:20.4714277 +08:00 Thursday L System.Logging.StringLogging[0] #51
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.LogError()
      【2025-7-24 11:19:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:19:21.6717045 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #29
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:19:21.6749002 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #51
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:11.3116214 +08:00 Thursday L System.Logging.StringLogging[0] #16
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:11——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Can't replace active reader.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:13.1516878 +08:00 Thursday L System.Logging.StringLogging[0] #34
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:11——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Can't replace active reader.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:13.1922339 +08:00 Thursday L System.Logging.StringLogging[0] #36
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:11——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Failed to read the result set.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:14.2784794 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #34
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Can't replace active reader.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:14.3483378 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #36
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      MySqlConnector.MySqlException (0x80004005): Failed to read the result set.
       ---> System.ArgumentOutOfRangeException: value must be between 0 and 1 (Parameter 'value')
         at MySqlConnector.Protocol.Serialization.ByteArrayReader.set_Offset(Int32 value) in /_/src/MySqlConnector/Protocol/Serialization/ByteArrayReader.cs:line 17
         at MySqlConnector.Protocol.Payloads.ColumnDefinitionPayload.Create(ResizableArraySegment`1 arraySegment) in /_/src/MySqlConnector/Protocol/Payloads/ColumnDefinitionPayload.cs:line 82
         at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 145
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:14.6873592 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #16
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: Can't replace active reader.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:18.5268284 +08:00 Thursday L System.Logging.StringLogging[0] #32
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:14——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:18.5872378 +08:00 Thursday L System.Logging.StringLogging[0] #33
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:13——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:20.1753490 +08:00 Thursday L System.Logging.StringLogging[0] #36
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:19——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:21.2762384 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #32
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:21.4290221 +08:00 Thursday L System.Logging.StringLogging[0] #34
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:19——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:21.9073971 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #33
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:22.0034612 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #36
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:23.2690883 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #34
      [Microsoft.Extensions.Logging.Abstractions.dll] void Microsoft.Extensions.Logging.LoggerExtensions.LogError(ILogger logger, Exception exception, string message, params object[] args)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:23.4969086 +08:00 Thursday L System.Logging.StringLogging[0] #19
      [Furion.Pure.dll] void Furion.Logging.StringLoggingPart.Log()
      【2025-7-24 11:49:21——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:23.7863034 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #19
      [Admin.NET.Application.dll] async Task Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync()
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:44.1394389 +08:00 Thursday L System.Logging.StringLogging[0] #37
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 11:49:43——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:49:44.4166394 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #37
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:52:08.6659725 +08:00 Thursday L System.Logging.StringLogging[0] #4
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 11:52:07——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:52:08.8374125 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #4
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:52:11.2695482 +08:00 Thursday L System.Logging.StringLogging[0] #34
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 11:52:11——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:52:11.4129635 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #34
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:54:43.4613678 +08:00 Thursday L System.Logging.StringLogging[0] #7
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 11:54:43——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 11:54:43.6439545 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #7
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:02:53.3703598 +08:00 Thursday L System.Logging.StringLogging[0] #19
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:02:52——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:02:54.1202751 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #19
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:04:19.1564455 +08:00 Thursday L System.Logging.StringLogging[0] #34
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:04:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:04:19.8331256 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #34
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:17:54.2018192 +08:00 Thursday L System.Logging.StringLogging[0] #31
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:17:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:17:55.9412247 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #31
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:17:56.0289392 +08:00 Thursday L System.Logging.StringLogging[0] #7
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:17:55——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:17:56.2506457 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #7
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:19:19.3509712 +08:00 Thursday L System.Logging.StringLogging[0] #32
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:19:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:19:20.2474017 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #32
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:32:53.8774215 +08:00 Thursday L System.Logging.StringLogging[0] #14
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:32:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:32:54.2190370 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #14
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:34:18.3222614 +08:00 Thursday L System.Logging.StringLogging[0] #56
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:34:17——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:34:19.7204402 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #56
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:47:53.8088568 +08:00 Thursday L System.Logging.StringLogging[0] #30
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:47:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:47:54.7425528 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #30
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:49:18.4146679 +08:00 Thursday L System.Logging.StringLogging[0] #57
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 12:49:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 12:49:19.4547142 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #57
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:02:54.0031617 +08:00 Thursday L System.Logging.StringLogging[0] #58
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:02:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:02:54.6708046 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #58
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:04:20.2400011 +08:00 Thursday L System.Logging.StringLogging[0] #55
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:04:19——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:04:21.0290621 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #55
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:17:54.1284215 +08:00 Thursday L System.Logging.StringLogging[0] #29
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:17:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:17:54.6433145 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #29
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:19:18.8377690 +08:00 Thursday L System.Logging.StringLogging[0] #31
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:19:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:19:19.3780612 +08:00 Thursday L System.Logging.StringLogging[0] #52
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:19:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:19:20.0644834 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #31
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:19:20.3670310 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #52
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:32:53.4776235 +08:00 Thursday L System.Logging.StringLogging[0] #29
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:32:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:32:54.2414595 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #29
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:34:19.3274230 +08:00 Thursday L System.Logging.StringLogging[0] #59
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:34:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:34:20.2790582 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #59
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:49:19.5603300 +08:00 Thursday L System.Logging.StringLogging[0] #31
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 13:49:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 13:49:20.3092653 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #31
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      System.InvalidOperationException: This MySqlConnection is already in use. See https://fl.vu/mysql-conn-reuse
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:02:53.5856341 +08:00 Thursday L System.Logging.StringLogging[0] #55
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:02:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:02:53.6727185 +08:00 Thursday L System.Logging.StringLogging[0] #31
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:02:53——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:02:54.3197374 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #55
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:02:54.8798126 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #31
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:04:19.1883549 +08:00 Thursday L System.Logging.StringLogging[0] #57
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:04:18——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:04:20.3806094 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #57
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:20.3704079 +08:00 Thursday L System.Logging.StringLogging[0] #61
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:19:20——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:20.5206904 +08:00 Thursday L System.Logging.StringLogging[0] #29
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:19:20——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:20.5972408 +08:00 Thursday L System.Logging.StringLogging[0] #30
      [Furion.Pure.dll] void Furion.Logging.Log.Error(string message, Exception exception, params object[] args)
      【2025-7-24 14:19:20——错误SQL】
      
      [Sql]:SELECT `ap_name`,`mac_address`,`ip_address`,`ap_location`,`ap_status`,`firmware_version`,`signal_strength`,`connected_devices_count`,`max_devices`,`IsDelete`,`CreateTime`,`UpdateTime`,`CreateUserId`,`CreateUserName`,`UpdateUserId`,`UpdateUserName`,`Id` FROM `access_points`  WHERE ( `IsDelete` = @IsDelete0 )  
      [Pars]:
      [Name]:@IsDelete0 [Value]:False [Type]:Boolean    
      
            
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:21.5512101 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #61
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:21.6710214 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #29
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 14:19:21.7129574 +08:00 Thursday L Admin.NET.Application.Service.APSyncBackgroundService[0] #30
      [System.Private.CoreLib.dll] void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<TResult>+AsyncStateMachineBox<TStateMachine>.MoveNext(Thread threadPoolThread)
      同步AP状态时发生异常      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      SqlSugar.SqlSugarException: Cannot Open when State is Connecting.
         at SqlSugar.MySqlProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
         at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
         at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
         at Admin.NET.Application.Service.APSyncBackgroundService.SyncAPStatusAsync() in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\APSyncBackgroundService.cs:line 99
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 16:15:40.1409547 +08:00 Thursday L Admin.NET.Application.AccessPointsService[0] #24 '00-0e5001ea5ed4e940e4baa96818853a60-c722d59bad38ddd8-00'
      [Admin.NET.Application.dll] async Task<bool> Admin.NET.Application.AccessPointsService.command(UpdateAPCommandInput updateAPCommandInput)
      AP网关命令下发时发生异常，MAC地址: (null), 命令: 0000      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Furion.FriendlyException.AppFriendlyException: MAC地址不能为空
         at Admin.NET.Application.AccessPointsService.command(UpdateAPCommandInput updateAPCommandInput) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\AccessPointsService.cs:line 470
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
fail: 2025-07-24 16:16:50.9421201 +08:00 Thursday L Admin.NET.Application.AccessPointsService[0] #23 '00-24bad4adf618d82475455f6aa39faa8c-a31b950596390b85-00'
      [Admin.NET.Application.dll] async Task<bool> Admin.NET.Application.AccessPointsService.command(UpdateAPCommandInput updateAPCommandInput)
      AP网关命令下发时发生异常，MAC地址: (null), 命令: 0000      
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
      Furion.FriendlyException.AppFriendlyException: MAC地址不能为空
         at Admin.NET.Application.AccessPointsService.command(UpdateAPCommandInput updateAPCommandInput) in D:\Project\电子桌牌\后台管理\Admin.NET\Admin.NET.Application\Service\AccessPoints\AccessPointsService.cs:line 470
      ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
