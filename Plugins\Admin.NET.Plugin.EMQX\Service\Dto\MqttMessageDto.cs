// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// MQTT消息DTO
/// </summary>
public class MqttMessageDto
{
    /// <summary>
    /// 消息ID
    /// </summary>
    public string MessageId { get; set; }

    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    public string Payload { get; set; }

    /// <summary>
    /// QoS级别
    /// </summary>
    public MqttQosLevelEnum QosLevel { get; set; }

    /// <summary>
    /// 是否保留消息
    /// </summary>
    public bool Retain { get; set; }

    /// <summary>
    /// 发送时间
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 发送者客户端ID
    /// </summary>
    public string SenderClientId { get; set; }

    /// <summary>
    /// 消息类型
    /// </summary>
    public MqttMessageTypeEnum MessageType { get; set; }

    /// <summary>
    /// 消息大小(字节)
    /// </summary>
    public int MessageSize { get; set; }
}

/// <summary>
/// MQTT发布消息输入DTO
/// </summary>
public class MqttPublishInput
{
    /// <summary>
    /// 主题
    /// </summary>
    [Required, Description("主题")]
    public string Topic { get; set; }

    /// <summary>
    /// 消息内容
    /// </summary>
    [Required, Description("消息内容")]
    public string Payload { get; set; }

    /// <summary>
    /// QoS级别
    /// </summary>
    [Description("QoS级别")]
    public MqttQosLevelEnum QosLevel { get; set; } = MqttQosLevelEnum.AtLeastOnce;

    /// <summary>
    /// 是否保留消息
    /// </summary>
    [Description("是否保留消息")]
    public bool Retain { get; set; } = false;

    /// <summary>
    /// 消息类型
    /// </summary>
    [Description("消息类型")]
    public MqttMessageTypeEnum MessageType { get; set; } = MqttMessageTypeEnum.CustomMessage;

    /// <summary>
    /// 客户端ID
    /// </summary>
    [Description("客户端ID")]
    public string ClientId { get; set; }

    /// <summary>
    /// 发送者客户端ID(可选)
    /// </summary>
    [Description("发送者客户端ID")]
    public string SenderClientId { get; set; }
}

/// <summary>
/// MQTT订阅输入DTO
/// </summary>
public class MqttSubscribeInput
{
    /// <summary>
    /// 主题
    /// </summary>
    [Required, Description("主题")]
    public string Topic { get; set; }

    /// <summary>
    /// QoS级别
    /// </summary>
    [Description("QoS级别")]
    public MqttQosLevelEnum QosLevel { get; set; } = MqttQosLevelEnum.AtLeastOnce;

    /// <summary>
    /// 客户端ID(可选，不指定则使用默认客户端)
    /// </summary>
    [Description("客户端ID")]
    public string ClientId { get; set; }
}

/// <summary>
/// MQTT取消订阅输入DTO
/// </summary>
public class MqttUnsubscribeInput
{
    /// <summary>
    /// 主题
    /// </summary>
    [Required, Description("主题")]
    public string Topic { get; set; }

    /// <summary>
    /// 客户端ID(可选，不指定则使用默认客户端)
    /// </summary>
    [Description("客户端ID")]
    public string ClientId { get; set; }
}

/// <summary>
/// MQTT订阅信息DTO
/// </summary>
public class MqttSubscriptionDto
{
    /// <summary>
    /// 主题
    /// </summary>
    public string Topic { get; set; }

    /// <summary>
    /// QoS级别
    /// </summary>
    public MqttQosLevelEnum QosLevel { get; set; }

    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; }

    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime SubscribeTime { get; set; }

    /// <summary>
    /// 接收消息数量
    /// </summary>
    public long ReceivedMessageCount { get; set; }
}

