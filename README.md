# 电子桌牌API

#### 介绍
电子桌牌API是一个基于.NET 8/9框架构建的现代化企业级后台管理系统，专为电子桌牌业务场景设计。系统采用微服务架构，提供完整的用户管理、权限控制、系统配置等功能，支持多租户、多语言，具备高性能、高安全性的特点。

#### 软件架构
- **技术栈**: .NET 8/9 + SqlSugar + Furion框架
- **数据库**: 支持MySQL、PostgreSQL、SqlServer、Oracle等多种数据库
- **缓存**: 集成Redis缓存，支持分布式缓存
- **认证**: 支持JWT、OAuth等多种认证方式
- **架构模式**: 采用DDD领域驱动设计，分层架构
- **部署方式**: 支持Docker容器化部署

#### 核心功能
- 🔐 **用户权限管理**: 完整的RBAC权限模型，支持用户、角色、菜单权限管理
- 🏢 **组织架构**: 支持多级组织架构，部门、职位管理
- 🔧 **系统配置**: 灵活的系统参数配置，字典数据管理
- 📊 **日志监控**: 操作日志、异常日志、访问日志全面记录
- 🔌 **插件系统**: 支持插件化扩展，包含钉钉、企业微信、数据可视化等插件
- 💳 **支付集成**: 支持支付宝、微信支付集成
- 🌐 **多租户**: 完整的多租户解决方案
- 🛡️ **安全加密**: 支持国密SM2、SM4算法

#### 安装教程

1.  **环境要求**
    - .NET 8.0 或 .NET 9.0 SDK
    - MySQL 5.7+ 或其他支持的数据库
    - Redis (可选，用于缓存)

2.  **数据库配置**
    - 修改 `Admin.NET.Application/Configuration/Database.json` 中的数据库连接字符串
    - 系统启动时会自动创建数据库表结构和初始数据

3.  **运行项目**
    - 设置 `Admin.NET.Web.Entry` 为启动项目
    - 运行项目，默认端口为 5005
    - 访问 http://localhost:5005 查看API文档

#### 使用说明

1.  **系统登录**
    - 默认超级管理员账号密码请查看数据库种子数据
    - 支持账号密码登录、第三方OAuth登录

2.  **权限配置**
    - 通过菜单管理配置系统功能菜单
    - 通过角色管理分配用户权限
    - 支持数据权限控制（全部、本部门、本部门及下级、仅本人）

3.  **插件使用**
    - 插件位于 `Plugins` 目录下
    - 可根据业务需要启用或禁用相应插件

## 更新日志

### v1.4.0 (2024-12-19)
- **编译错误修复**: 解决代码编译问题
  - 修复 `localAPs` 和 `localDevices` 变量未初始化错误
  - 修复 `hubContext` 变量名冲突问题
  - 完善 `IAPStatusHub` 接口，添加 `DeviceStatusUpdated` 方法
  - 增强异常处理和重试逻辑的完整性
  - 确保所有代码路径都有适当的错误处理

### v1.3.0 (2024-12-19)
- **数据库连接冲突修复**: 全面解决并发访问问题
  - 修复 `System.InvalidOperationException: Can't replace active reader` 错误
  - 修复 `MySqlConnector.MySqlException: Failed to read the result set` 错误
  - 修复 `SqlSugar.SqlSugarException: Cannot Open when State is Connecting` 错误
  - 改为串行执行AP和设备同步，避免并发冲突
  - 增强重试机制，支持多种异常类型的重试
  - 优化作用域管理，确保每个操作使用独立的数据库连接
  - 增加延迟机制，确保操作完全完成后再执行下一步

### v1.2.0 (2024-12-19)
- **SignalR推送优化**: 实现推送限流控制机制
  - 新增 `_apPushSemaphore` 和 `_devicePushSemaphore` 信号量控制
  - 防止并发推送冲突和Hub过载
  - 提升系统稳定性和推送可靠性
  - 优化资源释放和内存管理

### v1.1.0 (2024-12-19)
- **数据库连接优化**: 增强连接重试机制和作用域隔离
  - 实现数据库连接重试机制，解决 `SqlSugarException: Cannot Open when State is Connecting` 错误
  - 优化 `IServiceScope` 作用域管理，确保服务实例隔离
  - 增强异常处理和日志记录
  - 提升系统稳定性和容错能力

### v1.0.0 (2024-12-19)
- **核心功能**: AP和设备状态同步系统
  - 实现AP状态实时同步和监控
  - 实现蓝牙设备状态同步和管理
  - 集成SignalR实时推送功能
  - 支持第三方平台数据集成

## 常见问题

### 数据库连接错误

**问题1**: `SqlSugarException: Cannot Open when State is Connecting`
**问题2**: `System.InvalidOperationException: Can't replace active reader`
**问题3**: `MySqlConnector.MySqlException: Failed to read the result set`

**解决方案**:
- 系统已实现增强的重试机制，支持多种异常类型自动重试
- 采用串行执行模式，避免并发数据库访问冲突
- 每个操作使用独立的 `IServiceScope`，确保数据库连接隔离
- 增加操作间延迟，确保前一个操作完全完成
- 检查数据库连接字符串配置
- 确认数据库服务正常运行
- 检查网络连接状态
- 监控数据库连接池使用情况

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 特技

1.  使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
