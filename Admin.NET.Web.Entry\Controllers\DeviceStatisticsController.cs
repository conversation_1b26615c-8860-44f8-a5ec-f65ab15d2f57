// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Application;
using Microsoft.AspNetCore.Mvc;

namespace Admin.NET.Web.Entry.Controllers;

/// <summary>
/// 设备统计控制器示例
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class DeviceStatisticsController : ControllerBase
{
    private readonly DevicesService _devicesService;

    public DeviceStatisticsController(DevicesService devicesService)
    {
        _devicesService = devicesService;
    }

    /// <summary>
    /// 获取设备统计概览
    /// </summary>
    /// <returns></returns>
    [HttpGet("overview")]
    public async Task<IActionResult> GetStatisticsOverview()
    {
        try
        {
            var statistics = await _devicesService.GetDeviceStatistics();
            return Ok(new
            {
                success = true,
                data = statistics,
                message = "获取设备统计信息成功"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取设备详细统计信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("detailed")]
    public async Task<IActionResult> GetDetailedStatistics()
    {
        try
        {
            var statistics = await _devicesService.GetDeviceDetailedStatistics();
            return Ok(new
            {
                success = true,
                data = statistics,
                message = "获取设备详细统计信息成功"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }

    /// <summary>
    /// 根据条件获取设备统计信息
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns></returns>
    [HttpPost("by-condition")]
    public async Task<IActionResult> GetStatisticsByCondition([FromBody] DeviceStatisticsInput input)
    {
        try
        {
            var statistics = await _devicesService.GetDeviceStatisticsByCondition(input);
            return Ok(new
            {
                success = true,
                data = statistics,
                message = "获取条件设备统计信息成功"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取设备健康状况摘要
    /// </summary>
    /// <returns></returns>
    [HttpGet("health-summary")]
    public async Task<IActionResult> GetDeviceHealthSummary()
    {
        try
        {
            var statistics = await _devicesService.GetDeviceStatistics();
            
            // 计算健康状况
            var healthStatus = "良好";
            var issues = new List<string>();

            if (statistics.OnlineRate < 80)
            {
                healthStatus = "警告";
                issues.Add($"设备在线率较低：{statistics.OnlineRate}%");
            }

            if (statistics.LowBatteryRate > 20)
            {
                healthStatus = "警告";
                issues.Add($"低电量设备较多：{statistics.LowBatteryRate}%");
            }

            if (statistics.WeakSignalRate > 30)
            {
                healthStatus = "警告";
                issues.Add($"弱信号设备较多：{statistics.WeakSignalRate}%");
            }

            if (statistics.OnlineRate < 60 || statistics.LowBatteryRate > 40 || statistics.WeakSignalRate > 50)
            {
                healthStatus = "严重";
            }

            var summary = new
            {
                HealthStatus = healthStatus,
                TotalDevices = statistics.TotalDevices,
                OnlineRate = statistics.OnlineRate,
                Issues = issues,
                Recommendations = GetRecommendations(statistics)
            };

            return Ok(new
            {
                success = true,
                data = summary,
                message = "获取设备健康状况摘要成功"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }

    /// <summary>
    /// 根据统计数据生成建议
    /// </summary>
    /// <param name="statistics">统计数据</param>
    /// <returns></returns>
    private List<string> GetRecommendations(DeviceStatisticsOutput statistics)
    {
        var recommendations = new List<string>();

        if (statistics.OfflineDevices > 0)
        {
            recommendations.Add($"建议检查 {statistics.OfflineDevices} 台离线设备的网络连接");
        }

        if (statistics.LowBatteryDevices > 0)
        {
            recommendations.Add($"建议为 {statistics.LowBatteryDevices} 台设备充电或更换电池");
        }

        if (statistics.WeakSignalDevices > 0)
        {
            recommendations.Add($"建议检查 {statistics.WeakSignalDevices} 台设备的信号覆盖情况");
        }

        if (statistics.OnlineRate > 95 && statistics.LowBatteryRate < 5 && statistics.WeakSignalRate < 5)
        {
            recommendations.Add("设备运行状况良好，建议继续保持当前维护策略");
        }

        return recommendations;
    }

    /// <summary>
    /// 按会议室分组获取设备统计信息
    /// </summary>
    /// <param name="input">查询条件</param>
    /// <returns></returns>
    [HttpPost("by-meeting-room")]
    public async Task<IActionResult> GetStatisticsByMeetingRoom([FromBody] DeviceStatisticsInput input)
    {
        try
        {
            var statistics = await _devicesService.GetDeviceStatisticsByMeetingRoom(input);
            return Ok(new
            {
                success = true,
                data = statistics,
                message = "获取会议室设备统计信息成功"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取指定会议室的设备统计信息
    /// </summary>
    /// <param name="meetingRoomId">会议室ID，0表示所有会议室</param>
    /// <returns></returns>
    [HttpGet("meeting-room/{meetingRoomId}")]
    public async Task<IActionResult> GetMeetingRoomStatistics(long meetingRoomId)
    {
        try
        {
            var input = new DeviceStatisticsInput
            {
                MeetingRoomId = meetingRoomId
            };

            var statistics = await _devicesService.GetDeviceStatisticsByMeetingRoom(input);
            var result = statistics.FirstOrDefault();

            if (result == null)
            {
                return NotFound(new
                {
                    success = false,
                    message = "未找到指定会议室的设备统计信息"
                });
            }

            return Ok(new
            {
                success = true,
                data = result,
                message = "获取会议室设备统计信息成功"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new
            {
                success = false,
                message = ex.Message
            });
        }
    }
}
