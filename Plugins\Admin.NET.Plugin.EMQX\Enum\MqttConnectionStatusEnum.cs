// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// MQTT连接状态枚举
/// </summary>
public enum MqttConnectionStatusEnum
{
    /// <summary>
    /// 已断开
    /// </summary>
    [Description("已断开")]
    Disconnected = 0,

    /// <summary>
    /// 连接中
    /// </summary>
    [Description("连接中")]
    Connecting = 1,

    /// <summary>
    /// 已连接
    /// </summary>
    [Description("已连接")]
    Connected = 2,

    /// <summary>
    /// 重连中
    /// </summary>
    [Description("重连中")]
    Reconnecting = 3,

    /// <summary>
    /// 连接失败
    /// </summary>
    [Description("连接失败")]
    Failed = 4
}

/// <summary>
/// MQTT消息类型枚举
/// </summary>
public enum MqttMessageTypeEnum
{
    /// <summary>
    /// 设备状态
    /// </summary>
    [Description("设备状态")]
    DeviceStatus = 1,

    /// <summary>
    /// 设备数据
    /// </summary>
    [Description("设备数据")]
    DeviceData = 2,

    /// <summary>
    /// 设备控制
    /// </summary>
    [Description("设备控制")]
    DeviceControl = 3,

    /// <summary>
    /// 系统消息
    /// </summary>
    [Description("系统消息")]
    SystemMessage = 4,

    /// <summary>
    /// 自定义消息
    /// </summary>
    [Description("自定义消息")]
    CustomMessage = 5
}

/// <summary>
/// MQTT QoS级别枚举
/// </summary>
public enum MqttQosLevelEnum
{
    /// <summary>
    /// 最多一次
    /// </summary>
    [Description("最多一次")]
    AtMostOnce = 0,

    /// <summary>
    /// 至少一次
    /// </summary>
    [Description("至少一次")]
    AtLeastOnce = 1,

    /// <summary>
    /// 恰好一次
    /// </summary>
    [Description("恰好一次")]
    ExactlyOnce = 2
}