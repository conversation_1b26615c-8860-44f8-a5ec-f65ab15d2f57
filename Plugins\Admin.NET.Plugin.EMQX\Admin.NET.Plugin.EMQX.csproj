<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net8.0;net9.0</TargetFrameworks>
    <NoWarn>1701;1702;1591;8632</NoWarn>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <Copyright>Admin.NET</Copyright>
    <Description>Admin.NET EMQX MQTT服务插件</Description>
  </PropertyGroup>

  <ItemGroup>
    <None Update="Configuration\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Admin.NET.Core\Admin.NET.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MQTTnet" Version="4.3.3.952" />
    <PackageReference Include="MQTTnet.Extensions.ManagedClient" Version="4.3.3.952" />
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
  </ItemGroup>

</Project>