// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Plugin.EMQX;

/// <summary>
/// EMQX常量
/// </summary>
public class EMQXConst
{
    /// <summary>
    /// 客户端ID分隔符
    /// </summary>
    public const string CLIENT_ID_SEPARATOR = "@@@";

    /// <summary>
    /// 用户名分隔符
    /// </summary>
    public const string USERNAME_SEPARATOR = "|";

    /// <summary>
    /// 签名方法
    /// </summary>
    public const string SIGNATURE_METHOD = "hmacsha1";

    /// <summary>
    /// 签名版本
    /// </summary>
    public const string SIGNATURE_VERSION = "1.0";

    /// <summary>
    /// 默认QoS级别
    /// </summary>
    public const int DEFAULT_QOS = 1;

    /// <summary>
    /// 系统主题前缀
    /// </summary>
    public const string SYSTEM_TOPIC_PREFIX = "$SYS/";

    /// <summary>
    /// 设备状态主题
    /// </summary>
    public const string DEVICE_STATUS_TOPIC = "device/status";

    /// <summary>
    /// 设备数据主题
    /// </summary>
    public const string DEVICE_DATA_TOPIC = "device/data";

    /// <summary>
    /// 设备控制主题
    /// </summary>
    public const string DEVICE_CONTROL_TOPIC = "device/control";

    /// <summary>
    /// 连接状态：已连接
    /// </summary>
    public const string STATUS_CONNECTED = "connected";

    /// <summary>
    /// 连接状态：已断开
    /// </summary>
    public const string STATUS_DISCONNECTED = "disconnected";

    /// <summary>
    /// 连接状态：连接中
    /// </summary>
    public const string STATUS_CONNECTING = "connecting";

    /// <summary>
    /// 连接状态：重连中
    /// </summary>
    public const string STATUS_RECONNECTING = "reconnecting";
}