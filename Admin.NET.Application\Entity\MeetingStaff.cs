// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core;
using SqlSugar;
namespace Admin.NET.Application.Entity;

/// <summary>
/// 会议人员表
/// </summary>
[Tenant("1300000000001")]
[SugarTable("meeting_staff", "会议人员表")]
public partial class MeetingStaff : EntityBaseDel
{ 
    /// <summary>
    /// 员工编号
    /// </summary>
    [SugarColumn(ColumnName = "staff_id", ColumnDescription = "平台员工ID", Length = 50)]
    public virtual int? staff_id { get; set; }

    /// <summary>
    /// 员工编号
    /// </summary>
    [SugarColumn(ColumnName = "staff_code", ColumnDescription = "员工编号", Length = 50, IsNullable = true)]
    public virtual string? staff_code { get; set; }
    
    /// <summary>
    /// 员工姓名
    /// </summary>
    [SugarColumn(ColumnName = "staff_name", ColumnDescription = "员工姓名", Length = 100)]
    public virtual string? staff_name { get; set; }
    
    /// <summary>
    /// 职位
    /// </summary>
    [SugarColumn(ColumnName = "position", ColumnDescription = "职位", Length = 100)]
    public virtual string? position { get; set; }
    
    /// <summary>
    /// 部门
    /// </summary>
    [SugarColumn(ColumnName = "department", ColumnDescription = "部门", Length = 100)]
    public virtual string? department { get; set; }
    
    /// <summary>
    /// 邮箱
    /// </summary>
    [SugarColumn(ColumnName = "email", ColumnDescription = "邮箱", Length = 100)]
    public virtual string? email { get; set; }
    
    /// <summary>
    /// 手机号
    /// </summary>
    [SugarColumn(ColumnName = "phone", ColumnDescription = "手机号", Length = 20)]
    public virtual string? phone { get; set; }
    
    /// <summary>
    /// 头像
    /// </summary>
    [SugarColumn(ColumnName = "avatar_url", ColumnDescription = "头像", Length = 500)]
    public virtual string? avatar_url { get; set; }
    
    /// <summary>
    /// 绑定会议室ID
    /// </summary>
    [SugarColumn(ColumnName = "meeting_room_id", ColumnDescription = "绑定会议室ID")]
    public virtual long? meeting_room_id { get; set; }
    
    /// <summary>
    /// 自定义字段1
    /// </summary>
    [SugarColumn(ColumnName = "field1", ColumnDescription = "自定义字段1", Length = 255)]
    public virtual string? field1 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [SugarColumn(ColumnName = "field2", ColumnDescription = "自定义字段2", Length = 255)]
    public virtual string? field2 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [SugarColumn(ColumnName = "field3", ColumnDescription = "自定义字段2", Length = 255)]
    public virtual string? field3 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [SugarColumn(ColumnName = "field4", ColumnDescription = "自定义字段2", Length = 255)]
    public virtual string? field4 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [SugarColumn(ColumnName = "field5", ColumnDescription = "自定义字段2", Length = 255)]
    public virtual string? field5 { get; set; }
    
    /// <summary>
    /// 自定义字段2
    /// </summary>
    [SugarColumn(ColumnName = "field6", ColumnDescription = "自定义字段2", Length = 255)]
    public virtual string? field6 { get; set; }
    
    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(ColumnName = "description", ColumnDescription = "描述", Length = 255)]
    public virtual string? description { get; set; }
    
    /// <summary>
    /// 1：激活；2：未激活
    /// </summary>
    [SugarColumn(ColumnName = "meeting_staff_status", ColumnDescription = "1：激活；2：未激活")]
    public virtual int? meeting_staff_status { get; set; } = 1;
    
}
