// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;
using Furion.DatabaseAccessor;
using Furion.FriendlyException;
using Mapster;
using SqlSugar;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Admin.NET.Application.Entity;
namespace Admin.NET.Application;

/// <summary>
/// 设备绑定表服务 🧩
/// </summary>
[ApiDescriptionSettings(ApplicationConst.GroupName, Order = 100)]
public partial class DeviceBindingsService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DeviceBindings> _deviceBindingsRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public DeviceBindingsService(SqlSugarRepository<DeviceBindings> deviceBindingsRep, ISqlSugarClient sqlSugarClient)
    {
        _deviceBindingsRep = deviceBindingsRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询设备绑定表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询设备绑定表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DeviceBindingsOutput>> Page(PageDeviceBindingsInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _deviceBindingsRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.binding_data.ToString().Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.binding_data), u => u.binding_data.ToString().Contains(input.binding_data.Trim()))
            .WhereIF(input.device_id != null, u => u.device_id == input.device_id)
            .WhereIF(input.staff_id != null, u => u.staff_id == input.staff_id)
            .WhereIF(input.template_id != null, u => u.template_id == input.template_id)
            .WhereIF(input.meeting_room_id != null, u => u.meeting_room_id == input.meeting_room_id)
            .Select<DeviceBindingsOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取设备绑定表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取设备绑定表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DeviceBindings> Detail([FromQuery] QueryByIdDeviceBindingsInput input)
    {
        return await _deviceBindingsRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加设备绑定表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加设备绑定表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDeviceBindingsInput input)
    {
        var entity = input.Adapt<DeviceBindings>();
        return await _deviceBindingsRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新设备绑定表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新设备绑定表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDeviceBindingsInput input)
    {
        var entity = input.Adapt<DeviceBindings>();
        await _deviceBindingsRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除设备绑定表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除设备绑定表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDeviceBindingsInput input)
    {
        var entity = await _deviceBindingsRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _deviceBindingsRep.FakeDeleteAsync(entity);   //假删除
        //await _deviceBindingsRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除设备绑定表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除设备绑定表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDeviceBindingsInput> input)
    {
        var exp = Expressionable.Create<DeviceBindings>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _deviceBindingsRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _deviceBindingsRep.FakeDeleteAsync(list);   //假删除
        //return await _deviceBindingsRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出设备绑定表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出设备绑定表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDeviceBindingsInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDeviceBindingsOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "设备绑定表导出记录");
    }
    
    /// <summary>
    /// 下载设备绑定表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载设备绑定表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDeviceBindingsOutput>(), "设备绑定表导入模板");
    }
    
    private static readonly object _deviceBindingsImportLock = new object();
    /// <summary>
    /// 导入设备绑定表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入设备绑定表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (_deviceBindingsImportLock)
        {
            var stream = ExcelHelper.ImportData<ImportDeviceBindingsInput, DeviceBindings>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        return true;
                    }).Adapt<List<DeviceBindings>>();
                    
                    var storageable = _deviceBindingsRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.binding_data?.ToString()?.Length > 4000, "绑定数据长度不能超过4000个字符")
                        .SplitInsert(_=> true) // 没有设置唯一键代表插入所有数据
                        .ToStorage();
                    
                    storageable.AsInsertable.ExecuteCommand();// 不存在插入
                    storageable.AsUpdateable.UpdateColumns(it => new
                    {
                        it.device_id,
                        it.staff_id,
                        it.template_id,
                        it.meeting_room_id,
                        it.binding_data,
                    }).ExecuteCommand();// 存在更新
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
