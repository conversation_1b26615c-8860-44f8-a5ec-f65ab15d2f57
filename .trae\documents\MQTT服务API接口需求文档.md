## 1. Product Overview
MQTT服务API接口是一个基于Admin.NET框架的插件，提供EMQX和阿里云MQTT服务的统一接口管理功能。
该产品主要解决物联网设备与MQTT服务器的连接认证、消息发布订阅等核心功能，为电子桌牌等IoT设备提供可靠的消息通信服务。

## 2. Core Features

### 2.1 User Roles
| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| 系统管理员 | 默认管理员账户 | 可配置MQTT服务器、管理客户端连接、查看所有消息 |
| 设备操作员 | 管理员分配 | 可管理设备连接、发布订阅消息 |

### 2.2 Feature Module
我们的MQTT服务API接口需求包含以下主要页面：
1. **MQTT配置管理页面**: 服务器配置、认证参数设置、连接池管理
2. **客户端连接管理页面**: 客户端列表、连接状态监控、认证信息管理
3. **消息管理页面**: 消息发布、订阅管理、消息历史记录
4. **监控仪表板页面**: 连接统计、消息流量监控、系统状态展示

### 2.3 Page Details
| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| MQTT配置管理页面 | 服务器配置模块 | 配置EMQX和阿里云MQTT服务器地址、端口、SSL设置等连接参数 |
| MQTT配置管理页面 | 认证参数设置模块 | 设置客户端ID格式规则、用户名格式、阿里云签名算法配置 |
| MQTT配置管理页面 | 连接池管理模块 | 管理连接池大小、超时设置、重连策略配置 |
| 客户端连接管理页面 | 客户端列表模块 | 显示所有已连接客户端，支持按GroupID和ClientID筛选 |
| 客户端连接管理页面 | 连接状态监控模块 | 实时显示客户端连接状态、在线时长、最后活跃时间 |
| 客户端连接管理页面 | 认证信息管理模块 | 管理客户端认证信息，生成和验证阿里云签名密码 |
| 消息管理页面 | 消息发布模块 | 向指定主题发布消息，支持QoS级别设置 |
| 消息管理页面 | 订阅管理模块 | 管理主题订阅，设置订阅规则和权限 |
| 消息管理页面 | 消息历史记录模块 | 查看历史消息记录，支持按时间、主题、客户端筛选 |
| 监控仪表板页面 | 连接统计模块 | 显示总连接数、在线客户端数、连接成功率等统计信息 |
| 监控仪表板页面 | 消息流量监控模块 | 实时监控消息发送接收量、流量统计图表 |
| 监控仪表板页面 | 系统状态展示模块 | 显示MQTT服务器状态、系统资源使用情况 |

## 3. Core Process

### 管理员操作流程
1. 管理员登录系统后，首先进入MQTT配置管理页面配置服务器连接参数
2. 设置认证规则，包括客户端ID格式(GroupID@@@ClientID)和用户名格式(Signature|AccessKeyId|InstanceId)
3. 在客户端连接管理页面监控设备连接状态，管理认证信息
4. 通过消息管理页面发布系统消息或管理订阅规则
5. 在监控仪表板查看整体系统运行状态

### 设备操作员流程
1. 操作员登录后直接进入客户端连接管理页面查看负责的设备连接状态
2. 在消息管理页面发布设备相关消息或查看消息历史
3. 通过监控仪表板查看设备连接统计信息

```mermaid
graph TD
    A[MQTT配置管理页面] --> B[客户端连接管理页面]
    B --> C[消息管理页面]
    C --> D[监控仪表板页面]
    A --> D
    B --> D
```

## 4. User Interface Design

### 4.1 Design Style
- **主色调**: #1890ff (蓝色主题)，#52c41a (成功绿色)
- **辅助色**: #faad14 (警告黄色)，#f5222d (错误红色)
- **按钮样式**: 圆角按钮，支持主要、次要、危险等不同类型
- **字体**: 系统默认字体，标题16px，正文14px，说明文字12px
- **布局风格**: 卡片式布局，左侧导航菜单，顶部面包屑导航
- **图标风格**: 使用Ant Design图标库，简洁现代风格

### 4.2 Page Design Overview
| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| MQTT配置管理页面 | 服务器配置模块 | 表单卡片布局，输入框使用边框样式，配置项分组显示 |
| MQTT配置管理页面 | 认证参数设置模块 | 标签页切换不同认证方式，代码示例使用代码块样式 |
| 客户端连接管理页面 | 客户端列表模块 | 表格展示，支持分页和搜索，状态使用彩色标签显示 |
| 客户端连接管理页面 | 连接状态监控模块 | 实时状态使用进度条和状态点，在线状态用绿色圆点 |
| 消息管理页面 | 消息发布模块 | 分步骤表单，主题选择器，消息内容使用代码编辑器 |
| 消息管理页面 | 消息历史记录模块 | 时间轴样式展示消息，支持展开查看详细内容 |
| 监控仪表板页面 | 连接统计模块 | 数字卡片展示关键指标，使用图表库显示趋势 |
| 监控仪表板页面 | 消息流量监控模块 | 实时图表，使用ECharts展示流量曲线和柱状图 |

### 4.3 Responsiveness
产品采用桌面优先设计，支持平板和移动端自适应。在移动端，侧边栏收缩为抽屉式菜单，表格支持横向滚动，图表自动调整尺寸以适应小屏幕显示。